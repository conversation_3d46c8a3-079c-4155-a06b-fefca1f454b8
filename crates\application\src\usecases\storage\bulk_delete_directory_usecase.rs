use std::sync::Arc;

use reforged_domain::traits::storage_provider::StorageProvider;

use crate::error::ApplicationError;

pub struct BulkDeleteDirectoriesUsecase {
    storage_provider: Arc<dyn StorageProvider>,
}

impl BulkDeleteDirectoriesUsecase {
    pub fn new(storage_provider: Arc<dyn StorageProvider>) -> Self {
        Self { storage_provider }
    }
}

impl BulkDeleteDirectoriesUsecase {
    pub async fn execute(&self, directories: &[&str]) -> Result<(), ApplicationError> {
        let tasks = directories
            .into_iter()
            .map(|path| self.storage_provider.delete_directory(path));

        let results = futures_util::future::join_all(tasks).await;

        for result in results {
            result?;
        }

        Ok(())
    }
}
