use reforged_shared::{UuidId, Value};

use crate::models::directory::entity::Directory;

pub type DirectoryId = UuidId<Directory>;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, PartialEq, Eq, Default)]
pub struct DirectoryName(String);

impl DirectoryName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl From<String> for DirectoryName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for DirectoryName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

impl Value<String> for DirectoryName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Default)]
pub struct DirectoryPath(String);

impl DirectoryPath {
    pub fn new(path: impl Into<String>) -> Self {
        Self(path.into())
    }
}

impl From<String> for DirectoryPath {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for DirectoryPath {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

impl Value<String> for DirectoryPath {
    fn value(&self) -> String {
        self.0.clone()
    }
}
