use reforged_domain::{
    error::RepositoryError, models::store::entity::Store as DomainStore,
    models::store::value_object::StoreId, repository::store_repository::StoreRepository,
};
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::prelude::Decimal;
use sea_orm::{ActiveModelTrait, ModelTrait};
use sea_orm::{DatabaseConnection, EntityTrait, prelude::async_trait::async_trait};

use crate::models::stores::{ActiveModel, Entity};

pub struct PostgresStoreRepository {
    db: DatabaseConnection,
}

impl PostgresStoreRepository {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

#[async_trait]
impl StoreRepository for PostgresStoreRepository {
    async fn save(&self, store: &DomainStore) -> Result<(), RepositoryError> {
        let store_type = store.store_type().as_ref().map(|st| match st {
            reforged_domain::models::store::value_object::StoreType::Package => {
                crate::models::sea_orm_active_enums::StoreType::Package
            }
            reforged_domain::models::store::value_object::StoreType::VIP => {
                crate::models::sea_orm_active_enums::StoreType::Vip
            }
            reforged_domain::models::store::value_object::StoreType::FOUNDER => {
                crate::models::sea_orm_active_enums::StoreType::Founder
            }
            reforged_domain::models::store::value_object::StoreType::Coin => {
                crate::models::sea_orm_active_enums::StoreType::Coin
            }
        });

        let active_model = ActiveModel {
            id: Set(store.id().get_id()),
            available: Set(store.available().value().try_into().unwrap_or(0)),
            name: Set(store.name().value().clone()),
            price: Set(Decimal::from_f64_retain(store.price().value()).unwrap_or_default()),
            item: Set(store.item().value().try_into().unwrap_or(0)),
            achievement: Set(store.achievement().value().clone()),
            gold: Set(store.gold().value().try_into().unwrap_or(0)),
            coins: Set(store.coins().value().try_into().unwrap_or(0)),
            crystal: Set(store.crystal().value().try_into().unwrap_or(0)),
            diamonds: Set(store.diamonds().value().try_into().unwrap_or(0)),
            upgrade: Set(store.upgrade().value().try_into().unwrap_or(0)),
            role_id: Set(store.role_id().as_ref().map(|id| id.get_id())),
            title_id: Set(store.title_id().as_ref().map(|id| id.get_id())),
            bag_slots: Set(store.bag_slots().value().try_into().unwrap_or(0)),
            bank_slots: Set(store.bank_slots().value().try_into().unwrap_or(0)),
            house_slots: Set(store.house_slots().value().try_into().unwrap_or(0)),
            r#type: Set(store_type),
            quantity: Set(store.quantity().value().try_into().unwrap_or(0)),
            img: Set(store.img().value().clone()),
            ..Default::default()
        };

        active_model
            .insert(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn update(&self, store: &DomainStore) -> Result<(), RepositoryError> {
        let model = Entity::find_by_id(store.id().get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?
            .ok_or_else(|| RepositoryError::NotFound("Store not found".to_string()))?;

        let store_type = store.store_type().as_ref().map(|st| match st {
            reforged_domain::models::store::value_object::StoreType::Package => {
                crate::models::sea_orm_active_enums::StoreType::Package
            }
            reforged_domain::models::store::value_object::StoreType::VIP => {
                crate::models::sea_orm_active_enums::StoreType::Vip
            }
            reforged_domain::models::store::value_object::StoreType::FOUNDER => {
                crate::models::sea_orm_active_enums::StoreType::Founder
            }
            reforged_domain::models::store::value_object::StoreType::Coin => {
                crate::models::sea_orm_active_enums::StoreType::Coin
            }
        });

        let mut active_model: ActiveModel = model.into();
        active_model.available = Set(store.available().value().try_into().unwrap_or(0));
        active_model.name = Set(store.name().value().clone());
        active_model.price =
            Set(Decimal::from_f64_retain(store.price().value()).unwrap_or_default());
        active_model.item = Set(store.item().value().try_into().unwrap_or(0));
        active_model.achievement = Set(store.achievement().value().clone());
        active_model.gold = Set(store.gold().value().try_into().unwrap_or(0));
        active_model.coins = Set(store.coins().value().try_into().unwrap_or(0));
        active_model.crystal = Set(store.crystal().value().try_into().unwrap_or(0));
        active_model.diamonds = Set(store.diamonds().value().try_into().unwrap_or(0));
        active_model.upgrade = Set(store.upgrade().value().try_into().unwrap_or(0));
        active_model.role_id = Set(store.role_id().as_ref().map(|id| id.get_id()));
        active_model.title_id = Set(store.title_id().as_ref().map(|id| id.get_id()));
        active_model.bag_slots = Set(store.bag_slots().value().try_into().unwrap_or(0));
        active_model.bank_slots = Set(store.bank_slots().value().try_into().unwrap_or(0));
        active_model.house_slots = Set(store.house_slots().value().try_into().unwrap_or(0));
        active_model.r#type = Set(store_type);
        active_model.quantity = Set(store.quantity().value().try_into().unwrap_or(0));
        active_model.img = Set(store.img().value().clone());

        active_model
            .update(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn delete(&self, id: &StoreId) -> Result<(), RepositoryError> {
        let model = Entity::find_by_id(id.get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?
            .ok_or_else(|| RepositoryError::NotFound("Store not found".to_string()))?;

        model
            .delete(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }
}
