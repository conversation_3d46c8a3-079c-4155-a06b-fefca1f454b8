use reforged_domain::{
    error::RepositoryError, models::wheel_reward::entity::WheelReward as DomainWheelReward,
    models::wheel_reward::value_object::WheelRewardId,
    repository::wheel_reward_repository::WheelRewardRepository,
};
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::prelude::Decimal;
use sea_orm::{ActiveModelTrait, ModelTrait};
use sea_orm::{DatabaseConnection, EntityTrait, prelude::async_trait::async_trait};

use crate::models::wheel_rewards::{ActiveModel, Entity};

pub struct PostgresWheelRewardRepository {
    db: DatabaseConnection,
}

impl PostgresWheelRewardRepository {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

#[async_trait]
impl WheelRewardRepository for PostgresWheelRewardRepository {
    async fn save(&self, wheel_reward: &DomainWheelReward) -> Result<(), RepositoryError> {
        let active_model = ActiveModel {
            id: Set(wheel_reward.id().get_id()),
            item_id: Set(wheel_reward.item_id().get_id()),
            chance: Set(Decimal::from_f64_retain(wheel_reward.chance().value()).unwrap_or_default()),
            ..Default::default()
        };

        active_model
            .insert(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn update(&self, wheel_reward: &DomainWheelReward) -> Result<(), RepositoryError> {
        let model = Entity::find_by_id(wheel_reward.id().get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?
            .ok_or_else(|| RepositoryError::NotFound("WheelReward not found".to_string()))?;

        let mut active_model: ActiveModel = model.into();
        active_model.item_id = Set(wheel_reward.item_id().get_id());
        active_model.chance =
            Set(Decimal::from_f64_retain(wheel_reward.chance().value()).unwrap_or_default());

        active_model
            .update(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn delete(&self, id: &WheelRewardId) -> Result<(), RepositoryError> {
        let model = Entity::find_by_id(id.get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?
            .ok_or_else(|| RepositoryError::NotFound("WheelReward not found".to_string()))?;

        model
            .delete(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }
}
