use actix_web::{
    Scope,
    web::{self, <PERSON><PERSON>},
};
use reforged_application::{
    queries::item_rarities::handler::ItemRarityResponse,
    usecases::item_rarity::{
        get_item_rarities_usecase::GetItemRaritiesUsecase,
        get_item_rarity_by_id_usecase::GetItemRarityByIdUsecase,
    },
};

use crate::{error::ApiError, state::ApiState};

pub fn create_item_rarity_service() -> Scope {
    web::scope("/item-rarities")
        .service(web::resource("").route(web::get().to(get_all_item_rarities_handler)))
        .service(web::resource("/{id}").route(web::get().to(get_item_rarity_by_id_handler)))
}

async fn get_all_item_rarities_handler(
    state: web::Data<ApiState>,
) -> Result<Json<Vec<ItemRarityResponse>>, ApiError> {
    let usecase = GetItemRaritiesUsecase::new(state.item_rarity_query_handler().clone());

    let item_rarities = usecase.execute().await?;

    Ok(web::Json(item_rarities))
}

async fn get_item_rarity_by_id_handler(
    state: web::Data<ApiState>,
    id: web::Path<uuid::Uuid>,
) -> Result<Json<ItemRarityResponse>, ApiError> {
    let id = id.into_inner();
    let usecase = GetItemRarityByIdUsecase::new(state.item_rarity_query_handler().clone());

    let item_rarity = usecase.execute(id).await?;

    Ok(web::Json(item_rarity))
}
