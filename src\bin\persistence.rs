use std::sync::Arc;

use actix::Actor;
use lapin::{
    ExchangeKind,
    options::{ExchangeDeclareOptions, QueueBindOptions, QueueDeclareOptions},
    types::{AMQPValue, FieldTable, ShortString},
};
use reforged_application::traits::BrokerServiceConsumer;
use reforged_infrastructure::{
    Config,
    actors::{message_broker::actor::MessageBrokerActor, projection::actor::ProjectionActor},
    broker::MessageBrokerConfig,
    connection::create_pool,
    hashing::Argon2Hasher,
    loader::load_api_config,
    password_reset_token::write::PostgresPasswordResetTokenRepository,
    rabbit::RabbitMQConsumer,
    user::write::PostgresUserRepository,
};
use tracing::info;
use tracing_log::LogTracer;
use tracing_subscriber::{Layer, layer::SubscriberExt};

#[actix::main]
async fn main() -> anyhow::Result<()> {
    let crate_name = env!("CARGO_CRATE_NAME");
    let crate_version = env!("CARGO_PKG_VERSION");
    let name = format!("{} {}", crate_name, crate_version);

    setup_tracing();
    setup_rustls();

    let config = load_api_config()?;
    let database_config = config.database();

    let pool = create_pool(&database_config.get_connection_string()).await?;

    let broker_config = MessageBrokerConfig::from_env()?;
    let user_repository = PostgresUserRepository::new(pool.clone());
    let user_repository = Arc::new(user_repository);

    let password_reset_token_repository = PostgresPasswordResetTokenRepository::new(pool.clone()); // Added
    let password_reset_token_repository = Arc::new(password_reset_token_repository); // Added

    let password_hasher = Argon2Hasher::new();
    let password_hasher = Arc::new(password_hasher);

    let projection_actor = ProjectionActor::new(
        user_repository,
        password_hasher,
        password_reset_token_repository, // Added
    );
    projection_actor.start();

    let connection =
        lapin::Connection::connect(&broker_config.url, lapin::ConnectionProperties::default())
            .await?;
    let channel = connection.create_channel().await?;

    let mut exchange_options = ExchangeDeclareOptions::default();
    exchange_options.durable = true;

    let mut args = FieldTable::default();
    args.insert(ShortString::from("x-message-ttl"), AMQPValue::LongInt(30));

    channel
        .exchange_declare(
            &broker_config.exchange,
            ExchangeKind::Fanout,
            exchange_options,
            args.clone(),
        )
        .await?;

    let mut options = QueueDeclareOptions::default();
    options.durable = true;

    let queue_name = "reforged_bus_queue2";
    let consumer_name = "reforged_persistence_consumer";

    channel.queue_declare(queue_name, options, args).await?;

    channel
        .queue_bind(
            queue_name,
            &broker_config.exchange,
            "",
            QueueBindOptions::default(),
            FieldTable::default(),
        )
        .await?;

    let message_broker = MessageBrokerActor::new(name);
    let message_broker_addr = message_broker.start();

    let rabbit_mq = RabbitMQConsumer::new(
        consumer_name.to_string(),
        queue_name.to_string(),
        channel,
        message_broker_addr,
    );
    rabbit_mq.consume().await.map_err(|e| anyhow::anyhow!(e))?;

    Ok(())
}

pub fn setup_tracing() {
    let crate_name = env!("CARGO_CRATE_NAME");
    let crate_version = env!("CARGO_PKG_VERSION");

    let filter_layer = tracing_subscriber::EnvFilter::try_from_default_env().unwrap_or_else(|_| {
        format!("RUST_LOG=info,{}=info,reforged_api=info,reforged_infrastructure=info,reforged_application=info,tokio=trace,runtime=trace,actix_web=info", crate_name).into()
    });

    let fmt_layer = tracing_subscriber::fmt::layer().with_filter(filter_layer);
    let subscriber = tracing_subscriber::registry().with(fmt_layer);

    tracing::subscriber::set_global_default(subscriber)
        .expect("Failed to set global default subscriber");

    LogTracer::init().expect("Failed to set logger");

    info!("[REFORGED] {} v{}", crate_name, crate_version);
}

pub fn setup_rustls() {
    rustls::crypto::ring::default_provider()
        .install_default()
        .expect("Failed to install rustls crypto provider");
}
