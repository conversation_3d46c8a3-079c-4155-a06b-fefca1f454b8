use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_redeem::entity::UserRedeem;
use reforged_domain::models::user_redeem::value_object::UserRedeemId;
use reforged_domain::repository::user_redeem_repository::UserRedeemRepository;
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{
    ActiveModelTrait, DatabaseConnection, EntityTrait, IntoActiveModel,
    prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::models::user_redeems::{ActiveModel as UserRedeemsActiveModel, Entity as UserRedeems};

#[allow(dead_code)]
pub struct PostgresUserRedeemRepository {
    pool: DatabaseConnection,
}

impl PostgresUserRedeemRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserRedeemRepository for PostgresUserRedeemRepository {
    async fn save(&self, entity: &UserRedeem) -> Result<(), RepositoryError> {
        let model = UserRedeemsActiveModel {
            id: Set(entity.id().get_id()),
            user_id: Set(entity.user_id().get_id()),
            redeem_id: Set(entity.redeem_id().get_id()),
            date: Set(entity.date().value().naive_utc()),
        };

        model.insert(&self.pool).await.map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn update(&self, entity: &UserRedeem) -> Result<(), RepositoryError> {
        let existing_model = UserRedeems::find_by_id(entity.id().get_id())
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?
            .ok_or(RepositoryError::NotFound(format!(
                "UserRedeem with id {}",
                entity.id().get_id()
            )))?;

        let mut active_model = existing_model.into_active_model();

        active_model.user_id = Set(entity.user_id().get_id());
        active_model.redeem_id = Set(entity.redeem_id().get_id());
        active_model.date = Set(entity.date().value().naive_utc());

        active_model
            .update(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn delete(&self, id: &UserRedeemId) -> Result<(), RepositoryError> {
        let result = UserRedeems::delete_by_id(id.get_id())
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if result.rows_affected == 0 {
            return Err(RepositoryError::NotFound(format!(
                "UserRedeem with id {}",
                id.get_id()
            )));
        }

        Ok(())
    }
}
