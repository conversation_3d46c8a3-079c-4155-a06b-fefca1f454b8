use reforged_domain::error::RepositoryError;
use reforged_domain::models::redeem_code::value_object::RedeemCodeId;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::user_redeem::entity::UserRedeem;
use reforged_domain::models::user_redeem::value_object::UserRedeemId;
use reforged_domain::repository::user_redeem_repository::UserRedeemReadRepository;
use reforged_shared::IdTrait;
use sea_orm::{
    ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::mappers::user_redeem_mapper::UserRedeemDbModelMapper;
use crate::models::user_redeems::{Column as UserRedeemsColumn, Entity as UserRedeems};

#[allow(dead_code)]
pub struct PostgresUserRedeemReadRepository {
    pool: DatabaseConnection,
}

impl PostgresUserRedeemReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserRedeemReadRepository for PostgresUserRedeemReadRepository {
    async fn find_by_id(&self, id: &UserRedeemId) -> Result<Option<UserRedeem>, RepositoryError> {
        let user_redeem = UserRedeems::find_by_id(id.get_id())
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_redeem {
            Some(model) => {
                let mapped = UserRedeem::from(UserRedeemDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<UserRedeem>, RepositoryError> {
        let user_redeems = UserRedeems::find()
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserRedeem> = user_redeems
            .into_iter()
            .map(|model| UserRedeem::from(UserRedeemDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }

    async fn find_by_user_id(&self, user_id: &UserId) -> Result<Vec<UserRedeem>, RepositoryError> {
        let user_redeems = UserRedeems::find()
            .filter(UserRedeemsColumn::UserId.eq(user_id.get_id()))
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserRedeem> = user_redeems
            .into_iter()
            .map(|model| UserRedeem::from(UserRedeemDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }

    async fn find_by_redeem_code_id(
        &self,
        redeem_code_id: &RedeemCodeId,
    ) -> Result<Vec<UserRedeem>, RepositoryError> {
        let user_redeems = UserRedeems::find()
            .filter(UserRedeemsColumn::RedeemId.eq(redeem_code_id.get_id()))
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserRedeem> = user_redeems
            .into_iter()
            .map(|model| UserRedeem::from(UserRedeemDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }
}
