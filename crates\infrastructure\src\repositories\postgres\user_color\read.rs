use reforged_domain::error::RepositoryError;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::user_color::entity::UserColor;
use reforged_domain::models::user_color::value_object::UserColorId;
use reforged_domain::repository::user_color_repository::UserColorReadRepository;
use reforged_shared::IdTrait;
use sea_orm::{
    ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::mappers::user_color_mapper::UserColorDbModelMapper;
use crate::models::user_colors::{Column as UserColorsColumn, Entity as UserColors};

#[allow(dead_code)]
pub struct PostgresUserColorReadRepository {
    pool: DatabaseConnection,
}

impl PostgresUserColorReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserColorReadRepository for PostgresUserColorReadRepository {
    async fn find_by_id(&self, id: &UserColorId) -> Result<Option<UserColor>, RepositoryError> {
        let user_color = UserColors::find_by_id(id.get_id())
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_color {
            Some(model) => {
                let mapped = UserColor::from(UserColorDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<UserColor>, RepositoryError> {
        let user_colors = UserColors::find()
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserColor> = user_colors
            .into_iter()
            .map(|model| UserColor::from(UserColorDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }

    async fn find_by_user_id(
        &self,
        user_id: &UserId,
    ) -> Result<Option<UserColor>, RepositoryError> {
        let user_color = UserColors::find()
            .filter(UserColorsColumn::UserId.eq(user_id.get_id()))
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_color {
            Some(model) => {
                let mapped = UserColor::from(UserColorDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }
}
