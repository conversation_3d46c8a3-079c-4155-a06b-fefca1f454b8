use reforged_domain::error::RepositoryError;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::user_boost::entity::UserBoost;
use reforged_domain::models::user_boost::value_object::UserBoostId;
use reforged_domain::repository::user_boost_repository::UserBoostReadRepository;
use reforged_shared::IdTrait;
use sea_orm::{
    ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::mappers::user_boost_mapper::UserBoostDbModelMapper;
use crate::models::user_boosts::{Column as UserBoostsColumn, Entity as UserBoosts};

#[allow(dead_code)]
pub struct PostgresUserBoostReadRepository {
    pool: DatabaseConnection,
}

impl PostgresUserBoostReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserBoostReadRepository for PostgresUserBoostReadRepository {
    async fn find_by_id(&self, id: &UserBoostId) -> Result<Option<UserBoost>, RepositoryError> {
        let user_boost = UserBoosts::find()
            .filter(UserBoostsColumn::Id.eq(id.get_id()))
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_boost {
            Some(model) => {
                let mapped = UserBoost::from(UserBoostDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<UserBoost>, RepositoryError> {
        let user_boosts = UserBoosts::find()
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserBoost> = user_boosts
            .into_iter()
            .map(|model| UserBoost::from(UserBoostDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }

    async fn find_by_user_id(&self, user_id: &UserId) -> Result<Vec<UserBoost>, RepositoryError> {
        let user_boosts = UserBoosts::find()
            .filter(UserBoostsColumn::UserId.eq(user_id.get_id()))
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserBoost> = user_boosts
            .into_iter()
            .map(|model| UserBoost::from(UserBoostDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }
}
