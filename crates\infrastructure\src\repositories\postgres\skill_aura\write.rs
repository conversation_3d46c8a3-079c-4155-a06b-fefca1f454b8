use async_trait::async_trait;
use reforged_domain::models::skill_aura::value_object::SkillAuraId;
use reforged_domain::{
    error::RepositoryError, models::skill_aura::entity::SkillAura,
    repository::skill_aura_repository::SkillAuraRepository,
};
use reforged_shared::IdTrait;
use sea_orm::{ActiveModelTrait, ActiveValue::Set, DatabaseConnection};
use sea_orm::{ActiveValue, EntityTrait};

use crate::SeaORMErr;

use crate::models::skill_auras::ActiveModel as SkillAurasActiveModel;
use crate::models::skill_auras::Entity as SkillAuras;

#[allow(dead_code)]
pub struct PostgresSkillAuraRepository {
    pool: DatabaseConnection,
}

impl PostgresSkillAuraRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl SkillAuraRepository for PostgresSkillAuraRepository {
    async fn save(&self, skill_aura: &SkillAura) -> Result<(), RepositoryError> {
        let skill_aura_model = SkillAurasActiveModel {
            id: ActiveValue::NotSet,
            skill_id: Set(skill_aura.skill_id().get_id()),
            aura_id: Set(skill_aura.aura_id().get_id()),
        };

        skill_aura_model
            .insert(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn update(&self, skill_aura: &SkillAura) -> Result<(), RepositoryError> {
        let skill_aura_model = SkillAurasActiveModel {
            id: Set(skill_aura.id().get_id()),
            skill_id: Set(skill_aura.skill_id().get_id()),
            aura_id: Set(skill_aura.aura_id().get_id()),
        };

        let result = skill_aura_model
            .update(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if result.id == skill_aura.id().get_id() {
            Ok(())
        } else {
            Err(RepositoryError::NotFound(format!(
                "SkillAura with id {} not found",
                skill_aura.id().get_id()
            )))
        }
    }

    async fn delete(&self, skill_aura_id: &SkillAuraId) -> Result<(), RepositoryError> {
        let model = SkillAurasActiveModel {
            id: Set(skill_aura_id.get_id()),
            ..Default::default()
        };

        let result = SkillAuras::delete(model)
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if result.rows_affected == 0 {
            return Err(RepositoryError::NotFound(format!(
                "SkillAura with id {} not found",
                skill_aura_id.get_id()
            )));
        }

        Ok(())
    }
}
