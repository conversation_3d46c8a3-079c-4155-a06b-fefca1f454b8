# AGENTS.md - Guide for AI Agents Working on ReForged

Welcome, agent! This document provides guidance for working effectively with the ReForged codebase. Understanding these conventions and patterns will help you generate accurate and maintainable code.

## 1. Project Overview

ReForged is a Rust-based project, appearing to be a backend system, potentially for a game, given references to a "Game Server Diagram" and domain models like "User", "Item", "Guild", etc. It is a rewrite of a previous project ("DeVoid").

The project emphasizes a clean architecture with clear separation of concerns, leveraging modern Rust practices and asynchronous programming.

**Key Technologies:**
*   Rust (2024 Edition)
*   Actix (Web framework & Actor system)
*   Tokio (Asynchronous runtime)
*   PostgreSQL (Primary data store & Event Store)
*   RabbitMQ (Message broker)
*   `sea-orm` (ORM and migration tool)
*   `esrs` (Event Sourcing in Rust)
*   `serde` (Serialization/Deserialization)
*   `getset` (Getter/Setter generation)
*   `bon::Builder` (Builder pattern for domain entities)
*   `just` (Command runner, uses Nushell)
*   `tracing` (Logging and distributed tracing)

## 2. Architecture

The project follows a layered architecture, heavily influenced by **CQRS (Command Query Responsibility Segregation)** and **Event-Driven principles**.

*   **Modular Crates:** The codebase is divided into several Rust crates, each with a distinct responsibility:
    *   `reforged-domain`: Contains domain models (entities, value objects), business logic, and repository traits. This is the core of your application.
    *   `reforged-application`: Orchestrates use cases (application services). It defines commands, queries, and their handlers. It uses domain entities and repository traits.
    *   `reforged-infrastructure`: Provides concrete implementations for infrastructure concerns, such as database repositories (using `sea-orm` for PostgreSQL), message brokers, email services, etc. Implements repository traits defined in the domain layer.
    *   `reforged-api`: Exposes the application's functionality via an HTTP API (using Actix Web). Handles requests, validation, DTOs, and calls application use cases.
    *   `reforged-ws`: Handles WebSocket communication.
    *   `reforged-emulator`: Contains tools for emulation (details less clear from initial analysis).
    *   `reforged-shared`: Common utilities, traits (e.g., `Value<T>`, `UuidId<T>`), error types, and helper functions used across multiple crates.
*   **CQRS Implementation:**
    *   **Commands vs. Queries:** Application logic is separated into command handlers (for state changes) and query handlers (for data retrieval).
    *   **Repository Separation:** In `reforged-infrastructure`, PostgreSQL repositories are often split into `write.rs` (implementing command-related traits) and `read.rs` (implementing query-related traits) for each entity.
    *   **Event Sourcing:** The use of `esrs` and terms like `UserStoreManager`, `AggregateState`, and `handle_command` in `RegisterUsecase` point towards event sourcing for at least some parts of the system (e.g., user creation).
*   **Dependency Injection:** Dependencies (like repositories, services) are typically injected into use cases and API handlers using `Arc<dyn Trait>` or concrete handler types which themselves encapsulate `Arc`s. The `ApiState` struct in `reforged-api` serves as a container for shared services.

## 3. Development Workflow & Common Tasks

The `justfile` in the root directory defines common development tasks. Use `just <command>` to execute them. Ensure `nushell` is available as it's used by the `justfile`.

*   **Build Project:**
    ```bash
    cargo build
    ```
*   **Run Tests:**
    ```bash
    just test          # Run all tests
    just test-domain   # Run tests for the domain crate
    # ... and similar for application, api, shared
    ```
*   **Format Code:**
    ```bash
    just format-all    # Format all code
    # ... and similar for specific packages (format-domain, etc.)
    ```
*   **Run Application:**
    *   The main server:
        ```bash
        cargo run --bin server
        ```
    *   Run all services (server, notification, persistence - likely using `mprocs` or similar):
        ```bash
        just run-all
        ```
*   **Database Migrations (using `sea-orm-cli` via `cargo run --bin migrator`):**
    *   Apply pending migrations:
        ```bash
        just migrate
        # or cargo run --bin migrator up
        ```
    *   Reset database (drops all tables and re-applies migrations):
        ```bash
        just reset-db
        # or cargo run --bin migrator fresh
        ```
    *   Create a new migration:
        ```bash
        just add-migration <migration_name>
        # or sea-orm-cli migrate generate -d crates/infrastructure/src/persistence/database/migrations/ --universal-time <migration_name>
        ```
    *   Create a new seeder:
        ```bash
        just add-seeder <seeder_name>
        # or sea-orm-cli migrate generate -d crates/infrastructure/src/persistence/database/seeders/ --universal-time <seeder_name>
        ```
*   **Create New Entity (Scaffolding):**
    ```bash
    just create-entity <entity_name>
    ```
    This command will create the necessary directory structure and initial files for a new domain entity under `crates/domain/src/models/`. Remember to update `crates/domain/src/models/mod.rs` if not done automatically.

*   **Environment Variables:** Configuration is managed via a `.env` file. Copy `.env.example` to `.env` and update as needed.

## 4. Coding Conventions & Patterns

Adhere to the following conventions observed in the codebase:

*   **General Naming:**
    *   Structs, Enums, Traits: `PascalCase` (e.g., `User`, `UserRole`, `UserRepository`).
    *   Functions, Methods, Variables: `snake_case` (e.g., `execute_command`, `find_user_by_id`).
    *   Modules: `snake_case` (e.g., `user_repository`, `auth_handlers`).
    *   Type Aliases: `PascalCase` (e.g., `UserId`).
*   **Error Handling:**
    *   Use custom error enums for each layer (`DomainError`, `ApplicationError`, `RepositoryError`, `ApiError`). `thiserror` is used for deriving `std::error::Error`.
    *   Return `Result<T, YourErrorType>` from functions that can fail.
    *   Map errors appropriately when crossing layer boundaries (e.g., `DbErr` -> `RepositoryError`, `RepositoryError` -> `ApplicationError`).
*   **Asynchronous Code:**
    *   Use `async/await` for all I/O-bound operations.
    *   Use `#[async_trait::async_trait]` for traits with asynchronous methods.
*   **Domain Layer (`reforged-domain`):**
    *   **Entities:**
        *   Structs with fields representing value objects.
        *   Derive `#[derive(Debug, Clone, Default, bon::Builder, Getters, Setters)]`.
        *   Use `#[getset(get = "pub", set = "pub")]` for automatic getter/setter generation.
        *   Example: `crates/domain/src/models/user/entity.rs`.
    *   **Value Objects:**
        *   Newtype pattern: Structs wrapping primitive types (e.g., `pub struct Username(String);`).
        *   Derive common traits like `Debug`, `Clone`, `PartialEq`, `Eq`, `Ord`, `Default`.
        *   Implement `From<PrimitiveType>` and `reforged_shared::Value<PrimitiveType>`.
        *   Enums (like `UserRole`) should implement `Display`, `TryFrom`, and relevant `Into` conversions.
        *   Example: `crates/domain/src/models/user/value_object.rs`.
*   **Application Layer (`reforged-application`):**
    *   **Usecases:**
        *   Structs (e.g., `RegisterUsecase`) with a `new(...)` constructor for dependencies and an `async execute(...)` method.
        *   Dependencies are typically `Arc<dyn Trait>` or concrete handler types.
    *   **Commands/Queries:** Simple structs holding data for the operation.
    *   **Event Sourcing:** For commands that modify state, interact with aggregates and event stores (e.g., `UserStoreManager`, `AggregateState`).
*   **Infrastructure Layer (`reforged-infrastructure`):**
    *   **Repositories:**
        *   Implement traits defined in the `reforged-domain` layer.
        *   Use `sea-orm` for database operations.
        *   Follow CQRS: separate read logic (`read.rs`) from write logic (`write.rs`) for each entity.
        *   Use `sea-orm` active models for inserts/updates (`UsersActiveModel`).
        *   Use `EntityTrait::find()` and `QueryFilter` for queries.
        *   Map `sea-orm` models to domain entities using dedicated mapper structs/functions.
        *   Wrap `DbErr` from `sea-orm` into `RepositoryError`.
*   **API Layer (`reforged-api`):**
    *   **Handlers:** `async fn` functions taking `actix_web::web::Data<ApiState>` and other extractors (`Json`, `Path`).
    *   Instantiate and call application usecases.
    *   Use `ValidateJson<YourDto>` for request DTO validation.
    *   Return `Result<Json<ResponseType>, ApiError>`.
    *   Group related handlers into services using `web::scope(...)`.
    *   **DTOs:** Structs in `crates/api/src/dtos/` deriving `serde::Serialize`, `serde::Deserialize`, and `validator::Validate`.
*   **Shared Crate (`reforged_shared`):**
    *   Contains reusable components like `UuidId<T>`, `Value<T>` trait, UUID generators.
*   **UUIDs:** Prefer `uuid::Uuid::now_v7()` for generating new, time-ordered UUIDs.
*   **Testing:** Write unit tests within the same file as the code they test (`#[cfg(test)] mod tests { ... }`). Integration tests are in the `tests/integration` crate.

## 5. Key Files to Consult for Examples

*   **Domain Entity:** `crates/domain/src/models/user/entity.rs`
*   **Domain Value Object:** `crates/domain/src/models/user/value_object.rs`
*   **Application Usecase:** `crates/application/src/usecases/auth/register_usecase.rs`
*   **Infrastructure Write Repository:** `crates/infrastructure/src/repositories/postgres/user/write.rs`
*   **Infrastructure Read Repository:** `crates/infrastructure/src/repositories/postgres/user/read.rs`
*   **API Handler:** `crates/api/src/handlers/user_handlers.rs` or `crates/api/src/handlers/auth_handlers.rs`

By following these guidelines, you'll help maintain the quality and consistency of the ReForged codebase. If anything is unclear, refer to existing code for patterns. Happy coding!
