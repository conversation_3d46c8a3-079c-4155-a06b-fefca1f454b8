use std::sync::Arc;

use reforged_domain::traits::storage_provider::StorageProvider;

use crate::error::ApplicationError;

pub struct DeleteDirectoryUsecase {
    storage_provider: Arc<dyn StorageProvider>,
}

impl DeleteDirectoryUsecase {
    pub fn new(storage_provider: Arc<dyn StorageProvider>) -> Self {
        Self { storage_provider }
    }
}

impl DeleteDirectoryUsecase {
    pub async fn execute(&self, path: &str) -> Result<(), ApplicationError> {
        self.storage_provider.delete_directory(path).await?;

        Ok(())
    }
}
