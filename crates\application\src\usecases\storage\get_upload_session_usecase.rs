use std::sync::Arc;

use chrono::Duration;
use serde::{Deserialize, Serialize};

use crate::{
    error::ApplicationError,
    traits::{PasetoClaimPurpose, PasetoClaims, TokenService},
};

pub struct GetUploadSessionUsecase {
    token_service: Arc<dyn TokenService>,
}

impl GetUploadSessionUsecase {
    pub fn new(token_service: Arc<dyn TokenService>) -> Self {
        Self { token_service }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetUploadSessionResponse {
    file_upload_token: String,
    file_upload_token_expiry: String,
}

impl GetUploadSessionUsecase {
    pub async fn execute(
        &self,
        token: String,
    ) -> Result<GetUploadSessionResponse, ApplicationError> {
        let token_service = self.token_service.clone();
        let claims = token_service.validate_token(token, PasetoClaimPurpose::AccessToken)?;

        let file_upload_claims = PasetoClaims::new(
            claims.id,
            claims.email,
            claims.role,
            claims.exp,
            PasetoClaimPurpose::FileUpload,
        );

        let (file_upload_token, file_upload_token_expiry) =
            token_service.generate_token(file_upload_claims, Duration::minutes(30))?; // TODO: change to seconds

        let response = GetUploadSessionResponse {
            file_upload_token,
            file_upload_token_expiry: file_upload_token_expiry.to_rfc3339(),
        };

        Ok(response)
    }
}
