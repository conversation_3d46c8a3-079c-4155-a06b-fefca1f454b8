use reforged_domain::{
    error::RepositoryError,
    models::store::entity::Store as DomainStore,
    models::store::value_object::{StoreId, StoreType},
    repository::store_repository::StoreReadRepository,
};
use reforged_shared::IdTrait;
use sea_orm::{
    ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, prelude::async_trait::async_trait,
};

use crate::{
    mappers::store_mapper::StoreDbModelMapper,
    models::stores::{Column, Entity},
};

pub struct PostgresStoreReadRepository {
    db: DatabaseConnection,
}

impl PostgresStoreReadRepository {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

#[async_trait]
impl StoreReadRepository for PostgresStoreReadRepository {
    async fn find_by_id(&self, id: &StoreId) -> Result<Option<DomainStore>, RepositoryError> {
        let model = Entity::find_by_id(id.get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        match model {
            Some(model) => {
                let mapper = StoreDbModelMapper::new(model);
                Ok(Some(mapper.into()))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<DomainStore>, RepositoryError> {
        let models = Entity::find()
            .all(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        let stores = models
            .into_iter()
            .map(|model| {
                let mapper = StoreDbModelMapper::new(model);
                mapper.into()
            })
            .collect();

        Ok(stores)
    }

    async fn find_by_store_type(
        &self,
        store_type: &StoreType,
    ) -> Result<Vec<DomainStore>, RepositoryError> {
        let db_store_type = match store_type {
            StoreType::Package => crate::models::sea_orm_active_enums::StoreType::Package,
            StoreType::VIP => crate::models::sea_orm_active_enums::StoreType::Vip,
            StoreType::FOUNDER => crate::models::sea_orm_active_enums::StoreType::Founder,
            StoreType::Coin => crate::models::sea_orm_active_enums::StoreType::Coin,
        };

        let models = Entity::find()
            .filter(Column::Type.eq(db_store_type))
            .all(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        let stores = models
            .into_iter()
            .map(|model| {
                let mapper = StoreDbModelMapper::new(model);
                mapper.into()
            })
            .collect();

        Ok(stores)
    }

    async fn find_available(&self) -> Result<Vec<DomainStore>, RepositoryError> {
        let models = Entity::find()
            .filter(Column::Available.gt(0))
            .all(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        let stores = models
            .into_iter()
            .map(|model| {
                let mapper = StoreDbModelMapper::new(model);
                mapper.into()
            })
            .collect();

        Ok(stores)
    }
}
