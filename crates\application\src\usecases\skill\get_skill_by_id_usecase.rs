use reforged_domain::models::skill::value_object::SkillId;
use uuid::Uuid;

use crate::{
    error::ApplicationError,
    queries::{
        skills::handler::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SkillResponse},
        skills::queries::GetSkillByIdQuery,
    },
    traits::Que<PERSON><PERSON><PERSON><PERSON>,
};

pub struct GetSkillByIdUsecase {
    skill_query_handler: Ski<PERSON><PERSON>ueryHandler,
}

impl GetSkillByIdUsecase {
    pub fn new(skill_query_handler: <PERSON>ll<PERSON>ueryHandler) -> Self {
        Self {
            skill_query_handler,
        }
    }

    pub async fn execute(&self, id: Uuid) -> Result<SkillResponse, ApplicationError> {
        let id = SkillId::new(id);
        let query = GetSkillByIdQuery { id };
        let skill = self.skill_query_handler.handle(query).await?;

        Ok(skill)
    }
}
