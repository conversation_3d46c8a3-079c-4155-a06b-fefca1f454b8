# Refactoring Tasks for Clean Architecture Alignment

This document outlines the tasks needed to refactor the `reforged` project to better align with Clean Architecture principles, focusing on correctly distributing responsibilities and dependencies across the `domain`, `application`, `infrastructure`, and `api` crates.

## Backlog

### Domain Crate Refactoring

- [ ] **Define Aggregate Structures:** Define the structs/enums representing your core aggregates (e.g., `UserAggregate`) and their internal state within `reforged-domain`, likely under a new `src/aggregates/` module.
- [ ] **Move Event Definitions:** Move event structs/enums (e.g., `UserCreated`, `ResetPasswordRequested`, `UserPasswordChanged`) from `application/src/events/user/` into the `reforged-domain` crate, likely under the corresponding aggregate module (e.g., `src/aggregates/user/events.rs`).
- [ ] **Move Command Definitions:** Define command structs/enums (e.g., `CreateUserCommand`, `ChangeUserEmailCommand`) within the `reforged-domain` crate, likely under the corresponding aggregate module (e.g., `src/aggregates/user/commands.rs`). If existing command placeholders are in `application`, move their definitions here.
- [ ] **Define Aggregate Logic:** Implement the logic on your aggregate structs (e.g., `UserAggregate`) to handle commands and apply events. This might involve implementing traits from an Event Sourcing library.
- [ ] **Define Event Store and Aggregate Repository Traits:** Ensure the traits (`EventStore`, `AggregateRootRepository`) for saving and loading events and aggregates are defined in `reforged-domain::repository`. These traits should be generic over the aggregate and event types.
- [ ] **Define Read Repository Traits:** Define traits for read-optimized data access (e.g., `UserReadRepository`) in `reforged-domain::repository`. These traits should return domain or application-level DTOs.
- [ ] **Define UuidGenerator Trait (Optional but Recommended):** Define a `UuidGenerator` trait in `reforged-domain::traits` if ID generation is considered a domain concern.

### Infrastructure Crate Refactoring

- [ ] **Implement Event Store:** Implement the concrete `EventStore` trait (defined in domain) using `sea-orm` or a dedicated Event Store in `reforged-infrastructure/src/persistence/event_store.rs`.
- [ ] **Implement Aggregate Root Repository:** Implement the concrete `AggregateRootRepository` trait (defined in domain) in `reforged-infrastructure/src/persistence/repositories/aggregate_repo.rs`. This implementation will use the concrete `EventStore`.
- [ ] **Implement Read Repositories (Projections):** Implement the concrete `UserReadRepository` trait (defined in domain) using `sea-orm` or other data sources optimized for reads in `reforged-infrastructure/src/persistence/repositories/read_models/user_read_repo.rs`.
- [ ] **Implement Read Model Projectors (Event Handlers):** Create components within `reforged-infrastructure` that subscribe to events from the message broker and update the read database schema using the concrete read repository implementations. This is where the logic from `application::events::user::handlers.rs` will move.
- [ ] **Move Concrete Service Implementations:** Move concrete implementations of application service traits (e.g., `Captcha`, `EmailService`, `TokenService`, `PasswordHasher`, `BrokerServicePublisher/Consumer`) into the `reforged-infrastructure` crate, under appropriate modules like `src/services/` or `src/security/`.
- [ ] **Move Actix Actors:** Move the `reforged-application::actors` module and its contents (`email_notifier`, `message_broker`) into the `reforged-infrastructure` crate (e.g., `reforged-infrastructure/src/actors/`). Update internal imports.
- [ ] **Implement UuidGenerator:** Create a concrete implementation for the `UuidGenerator` trait (defined in domain) within the `reforged-infrastructure` crate.

### Application Crate Refactoring

- [ ] **Remove Infrastructure Dependencies:** Update `reforged-application/Cargo.toml` to remove direct dependencies on libraries whose concrete implementations are now solely in `reforged-infrastructure` (e.g., `sea-orm`, `lapin`, direct `actix` dependencies related to actors, direct `uuid` dependency if generation is moved to a trait).
- [ ] **Remove Command Placeholders:** Delete or refactor the placeholder command files/modules in `application::commands` now that command definitions belong in the domain.
- [ ] **Remove Actor Module:** Delete the `src/actors/` module from `reforged-application` after its contents are moved to `infrastructure`.
- [ ] **Remove Events Module and Handlers:** Delete the `src/events/` module from `reforged-application` after event definitions are moved to `domain` and handler *implementations* are moved to `infrastructure`.
- [ ] **Update Usecases (Command Handlers):** Refactor use cases (`src/usecases/`) to act as command handlers. They should: receive application-level inputs, translate to domain commands, load aggregates using the `AggregateRootRepository` trait, call aggregate command handling logic, save new events using the repository trait, and publish events using the `BrokerServicePublisher` trait.
- [ ] **Update Query Handlers:** Refactor query handlers (`src/queries/`) to use the *read* repository traits (defined in domain) to fetch data from the read model, returning application-level DTOs.
- [ ] **Update ID Generation:** Refactor use cases that generate new entity IDs to use the injected `Arc<dyn UuidGenerator>` trait object (defined in domain) instead of calling `uuid::Uuid::new_v7()` directly.
- [ ] **Organize Usecases and Queries (Optional):** Create sub-modules within `src/usecases/` and `src/queries/` (e.g., `user/`, `class_category/`) to group related handlers.

### API Crate Refactoring

- [ ] **Add Infrastructure Dependency:** Ensure `reforged-api/Cargo.toml` has a dependency on `reforged-infrastructure`.
- [ ] **Refine `ApiState`:** Modify the `reforged-api::state::ApiState` struct. It should primarily hold the instantiated application layer use case and query handler structs (`Arc<UsecaseStruct>`, `Arc<QueryHandlerStruct>`) rather than holding infrastructure traits or domain repository traits directly (as the use cases/query handlers will hold those). It can still hold API-specific configuration like `allowed_origins`.
- [ ] **Update Handlers and Middleware:** Ensure handlers and middleware in `reforged-api::handlers` and `reforged-api::middlewares` access application logic via the use case/query handler structs held in `ApiState`, or via the injected `Arc<dyn Trait>` references if `ApiState` is structured that way.
- [ ] **Update DTO to Command Mapping:** Handlers receiving API DTOs should map them to the appropriate domain command structures defined in the `domain` crate.

### Composition Root Refactoring (`main.rs` - Needs to be provided or inferred)

- [ ] **Instantiate Infrastructure:** In the main application entry point, instantiate all the concrete service and repository implementations from `reforged-infrastructure`.
- [ ] **Instantiate Application Components:** Instantiate the application layer Query Handler and Use Case structs, injecting the corresponding `Arc<dyn Trait>` instances from infrastructure into their constructors.
- [ ] **Build `ApiState`:** Create the `reforged-api::state::ApiState` instance, injecting the instantiated application layer components and any API-specific configurations.
- [ ] **Wire into Router:** Pass the fully constructed `ApiState` as `web::Data` when configuring the Actix-web `App` in `reforged-api::router`.\n- [ ] **Start Infrastructure Services:** Ensure long-running infrastructure services like the message broker event consumers (projections/event handlers) are instantiated and started as background tasks here.

### General Code Review

- [ ] **Review Traits:** Revisit all traits defined in `reforged-domain` and `reforged-application` to ensure they accurately represent the required contracts and do not contain implementation details specific to `reforged-infrastructure`.
- [ ] **Review Infrastructure Structure (Optional):** Further organize modules within `reforged-infrastructure` as needed (e.g., separating database access from external API clients, grouping projection update logic).

---

*This document was generated based on a code review and discussion regarding Clean Architecture and CQRS/Event Sourcing patterns.*