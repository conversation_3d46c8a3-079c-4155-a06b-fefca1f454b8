use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_color::entity::UserColor;
use reforged_domain::models::user_color::value_object::UserColorId;
use reforged_domain::repository::user_color_repository::UserColorRepository;
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{
    ActiveModelTrait, DatabaseConnection, EntityTrait, IntoActiveModel,
    prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::models::user_colors::{ActiveModel as UserColorsActiveModel, Entity as UserColors};

#[allow(dead_code)]
pub struct PostgresUserColorRepository {
    pool: DatabaseConnection,
}

impl PostgresUserColorRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserColorRepository for PostgresUserColorRepository {
    async fn save(&self, entity: &UserColor) -> Result<(), RepositoryError> {
        let model = UserColorsActiveModel {
            id: Set(entity.id().get_id()),
            user_id: Set(entity.user_id().get_id()),
            color_chat: Set(entity.color_chat().value()),
            color_name: Set(entity.color_name().value()),
            color_hair: Set(entity.color_hair().value()),
            color_skin: Set(entity.color_skin().value()),
            color_eye: Set(entity.color_eye().value()),
            color_base: Set(entity.color_base().value()),
            color_trim: Set(entity.color_trim().value()),
            color_accessory: Set(entity.color_accessory().value()),
        };

        model.insert(&self.pool).await.map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn update(&self, entity: &UserColor) -> Result<(), RepositoryError> {
        let existing_model = UserColors::find_by_id(entity.id().get_id())
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?
            .ok_or(RepositoryError::NotFound(format!(
                "UserColor with id {}",
                entity.id().get_id()
            )))?;

        let mut active_model = existing_model.into_active_model();

        active_model.user_id = Set(entity.user_id().get_id());
        active_model.color_chat = Set(entity.color_chat().value());
        active_model.color_name = Set(entity.color_name().value());
        active_model.color_hair = Set(entity.color_hair().value());
        active_model.color_skin = Set(entity.color_skin().value());
        active_model.color_eye = Set(entity.color_eye().value());
        active_model.color_base = Set(entity.color_base().value());
        active_model.color_trim = Set(entity.color_trim().value());
        active_model.color_accessory = Set(entity.color_accessory().value());

        active_model
            .update(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn delete(&self, id: &UserColorId) -> Result<(), RepositoryError> {
        let result = UserColors::delete_by_id(id.get_id())
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if result.rows_affected == 0 {
            return Err(RepositoryError::NotFound(format!(
                "UserColor with id {}",
                id.get_id()
            )));
        }

        Ok(())
    }
}
