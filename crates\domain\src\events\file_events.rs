use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct FileUploadStarted {
    pub id: uuid::Uuid,
    pub filename: String,
    pub file_size: usize,
    pub started_at: DateTime<Utc>,
    pub uploaded_by: uuid::Uuid,
}

// Example FileUploadEvents to demonstrate the scalable approach
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct FileUploaded {
    pub id: uuid::Uuid,
    pub filename: String,
    pub file_size: usize,
    pub uploaded_by: uuid::Uuid,
    pub uploaded_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct FileUploadProgress {
    pub id: uuid::Uuid,
    pub filename: String,
    pub uploaded_by: uuid::Uuid,
    pub bytes_uploaded: usize,
    pub file_size: usize,
}

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct FileUploadFailed {
    pub id: uuid::Uuid,
    pub filename: String,
    pub error_message: String,
    pub uploaded_by: uuid::Uuid,
}

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct FileDeleted {
    pub id: uuid::Uuid,
    pub filename: String,
    pub uploaded_by: uuid::Uuid,
    pub deleted_by: uuid::Uuid,
    pub deleted_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(tag = "event_type")]
pub enum FileUploadEvents {
    FileUploadStarted(FileUploadStarted),
    FileUploadProgress(FileUploadProgress),
    FileUploaded(FileUploaded),
    FileDeleted(FileDeleted),
    FileUploadFailed(FileUploadFailed),
}
