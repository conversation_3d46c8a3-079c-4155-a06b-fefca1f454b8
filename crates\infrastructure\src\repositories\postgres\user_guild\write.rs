use num_traits::ToPrimitive;
use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_guild::entity::UserGuild;
use reforged_domain::models::user_guild::value_object::UserGuildId;
use reforged_domain::repository::user_guild_repository::UserGuildRepository;
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{
    ActiveModelTrait, DatabaseConnection, EntityTrait, IntoActiveModel,
    prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::models::user_guilds::{ActiveModel as UserGuildsActiveModel, Entity as UserGuilds};

#[allow(dead_code)]
pub struct PostgresUserGuildRepository {
    pool: DatabaseConnection,
}

impl PostgresUserGuildRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserGuildRepository for PostgresUserGuildRepository {
    async fn save(&self, entity: &UserGuild) -> Result<(), RepositoryError> {
        let model = UserGuildsActiveModel {
            id: Set(entity.id().get_id()),
            user_id: Set(entity.user_id().get_id()),
            guild_id: Set(entity.guild_id().get_id()),
            rank: Set(entity.rank().value().to_i16().unwrap_or_default()),
        };

        model.insert(&self.pool).await.map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn update(&self, entity: &UserGuild) -> Result<(), RepositoryError> {
        let existing_model = UserGuilds::find_by_id(entity.id().get_id())
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?
            .ok_or(RepositoryError::NotFound(format!(
                "UserGuild with id {}",
                entity.id().get_id()
            )))?;

        let mut active_model = existing_model.into_active_model();

        active_model.user_id = Set(entity.user_id().get_id());
        active_model.guild_id = Set(entity.guild_id().get_id());
        active_model.rank = Set(entity.rank().value().to_i16().unwrap_or_default());

        active_model
            .update(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn delete(&self, id: &UserGuildId) -> Result<(), RepositoryError> {
        let result = UserGuilds::delete_by_id(id.get_id())
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if result.rows_affected == 0 {
            return Err(RepositoryError::NotFound(format!(
                "UserGuild with id {}",
                id.get_id()
            )));
        }

        Ok(())
    }
}
