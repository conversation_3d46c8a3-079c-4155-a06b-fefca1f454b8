use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_title::entity::UserTitle;
use reforged_domain::models::user_title::value_object::UserTitleId;
use reforged_domain::repository::user_title_repository::UserTitleRepository;
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{
    ActiveModelTrait, DatabaseConnection, EntityTrait, IntoActiveModel,
    prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::models::user_titles::{ActiveModel as UserTitlesActiveModel, Entity as UserTitles};

#[allow(dead_code)]
pub struct PostgresUserTitleRepository {
    pool: DatabaseConnection,
}

impl PostgresUserTitleRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserTitleRepository for PostgresUserTitleRepository {
    async fn save(&self, entity: &UserTitle) -> Result<(), RepositoryError> {
        let model = UserTitlesActiveModel {
            id: Set(entity.id().get_id()),
            user_id: Set(entity.user_id().get_id()),
            title_id: Set(entity.title_id().get_id()),
            date: Set(entity.date().value().naive_utc()),
        };

        model.insert(&self.pool).await.map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn update(&self, entity: &UserTitle) -> Result<(), RepositoryError> {
        let existing_model = UserTitles::find_by_id(entity.id().get_id())
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?
            .ok_or(RepositoryError::NotFound(format!(
                "UserTitle with id {}",
                entity.id().get_id()
            )))?;

        let mut active_model = existing_model.into_active_model();

        active_model.user_id = Set(entity.user_id().get_id());
        active_model.title_id = Set(entity.title_id().get_id());
        active_model.date = Set(entity.date().value().naive_utc());

        active_model
            .update(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn delete(&self, id: &UserTitleId) -> Result<(), RepositoryError> {
        let result = UserTitles::delete_by_id(id.get_id())
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if result.rows_affected == 0 {
            return Err(RepositoryError::NotFound(format!(
                "UserTitle with id {}",
                id.get_id()
            )));
        }

        Ok(())
    }
}
