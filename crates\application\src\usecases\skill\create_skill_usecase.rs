use std::sync::Arc;

use reforged_domain::{
    models::skill::{
        entity::Skill,
        value_object::{
            Animation, Chance, Cooldown, Damage, Dsrc, Effects, HitTargets, Icon, LifeSteal, Mana,
            ManaBack, Pet, Range, Reference, ShowDamage, SkillDescription, SkillId, SkillName,
            SkillType, Strl, Target,
        },
    },
    repository::skill_repository::SkillRepository,
};
use reforged_shared::uuid_generator::uuid_now;

use crate::{
    error::ApplicationError,
    queries::skills::{
        handler::{SkillQueryHandler, SkillResponse},
        queries::GetSkillByNameQuery,
    },
    traits::QueryHand<PERSON>,
};

#[derive(Debug, Clone)]
pub struct CreateSkillRequest {
    pub name: String,
    pub animation: String,
    pub description: String,
    pub damage: f64,
    pub mana: i16,
    pub mana_back: i16,
    pub life_steal: f64,
    pub icon: String,
    pub range: i32,
    pub dsrc: String,
    pub reference: String,
    pub target: String,
    pub effects: String,
    pub skill_type: String,
    pub strl: String,
    pub cooldown: i32,
    pub hit_targets: i16,
    pub pet: bool,
    pub chance: Option<f64>,
    pub show_damage: bool,
}

pub struct CreateSkillUsecase {
    skill_query_handler: SkillQueryHandler,
    skill_repository: Arc<dyn SkillRepository>,
}

impl CreateSkillUsecase {
    pub fn new(
        skill_query_handler: SkillQueryHandler,
        skill_repository: Arc<dyn SkillRepository>,
    ) -> Self {
        Self {
            skill_query_handler,
            skill_repository,
        }
    }
}

impl CreateSkillUsecase {
    pub async fn execute(
        &self,
        request: CreateSkillRequest,
    ) -> Result<SkillResponse, ApplicationError> {
        let query = GetSkillByNameQuery {
            name: request.name.clone(),
        };
        let skill = self.skill_query_handler.handle(query).await;
        if skill.is_ok() && skill.as_ref().unwrap().is_some() {
            return Err(ApplicationError::EntityAlreadyExists(request.name.clone()));
        }

        let new_skill = Skill::new(
            SkillId::new(uuid_now()),
            SkillName::new(request.name),
            Animation::new(request.animation),
            SkillDescription::new(request.description),
            Damage::new(request.damage),
            Mana::new(request.mana),
            ManaBack::new(request.mana_back),
            LifeSteal::new(request.life_steal),
            Icon::new(request.icon),
            Range::new(request.range),
            Dsrc::new(request.dsrc),
            Reference::new(request.reference),
            Target::new(request.target),
            Effects::new(request.effects),
            SkillType::new(request.skill_type),
            Strl::new(request.strl),
            Cooldown::new(request.cooldown),
            HitTargets::new(request.hit_targets),
            Pet::new(request.pet),
            Chance::new(request.chance),
            ShowDamage::new(request.show_damage),
        );

        let skill = self.skill_repository.save(&new_skill).await?;
        let skill = SkillResponse::from(skill);

        Ok(skill)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use mockall::predicate::*;
    use reforged_domain::repository::skill_repository::{
        MockSkillReadRepository, MockSkillRepository,
    };
    use std::sync::Arc;

    #[tokio::test]
    async fn test_create_skill_success() {
        // Arrange
        let mut mock_read_repo = MockSkillReadRepository::new();
        let mut mock_write_repo = MockSkillRepository::new();

        // Mock that skill doesn't exist
        mock_read_repo
            .expect_find_by_name()
            .with(eq("Test Skill"))
            .times(1)
            .returning(|_| Ok(None));

        // Mock successful save
        mock_write_repo
            .expect_save()
            .times(1)
            .returning(|skill| Ok(skill.clone()));

        let skill_query_handler = SkillQueryHandler::new(Arc::new(mock_read_repo));
        let usecase = CreateSkillUsecase::new(skill_query_handler, Arc::new(mock_write_repo));

        // Act
        let request = CreateSkillRequest {
            name: "Test Skill".to_string(),
            animation: "test_animation".to_string(),
            description: "Test Description".to_string(),
            damage: 100.0,
            mana: 50,
            mana_back: 10,
            life_steal: 5.0,
            icon: "test_icon.png".to_string(),
            range: 10,
            dsrc: "test_dsrc".to_string(),
            reference: "test_reference".to_string(),
            target: "test_target".to_string(),
            effects: "test_effects".to_string(),
            skill_type: "test_type".to_string(),
            strl: "test_strl".to_string(),
            cooldown: 30,
            hit_targets: 3,
            pet: false,
            chance: Some(75.0),
            show_damage: true,
        };

        let result = usecase.execute(request).await;

        // Assert
        assert!(result.is_ok());
        let skill = result.unwrap();
        assert_eq!(skill.name, "Test Skill");
        assert_eq!(skill.damage, 100.0);
        assert_eq!(skill.mana, 50);
    }

    #[tokio::test]
    async fn test_create_skill_already_exists() {
        // Arrange
        let mut mock_read_repo = MockSkillReadRepository::new();
        let mock_write_repo = MockSkillRepository::new();

        // Mock that skill already exists
        mock_read_repo
            .expect_find_by_name()
            .with(eq("Existing Skill"))
            .times(1)
            .returning(|_| {
                Ok(Some(Skill::new(
                    SkillId::new(uuid::Uuid::now_v7()),
                    SkillName::new("Existing Skill"),
                    Animation::new("animation"),
                    SkillDescription::new("description"),
                    Damage::new(50.0),
                    Mana::new(25),
                    ManaBack::new(5),
                    LifeSteal::new(2.5),
                    Icon::new("icon.png"),
                    Range::new(5),
                    Dsrc::new("dsrc"),
                    Reference::new("reference"),
                    Target::new("target"),
                    Effects::new("effects"),
                    SkillType::new("type"),
                    Strl::new("strl"),
                    Cooldown::new(15),
                    HitTargets::new(1),
                    Pet::new(false),
                    Chance::new(None),
                    ShowDamage::new(true),
                )))
            });

        let skill_query_handler = SkillQueryHandler::new(Arc::new(mock_read_repo));
        let usecase = CreateSkillUsecase::new(skill_query_handler, Arc::new(mock_write_repo));

        // Act
        let request = CreateSkillRequest {
            name: "Existing Skill".to_string(),
            animation: "test_animation".to_string(),
            description: "Test Description".to_string(),
            damage: 100.0,
            mana: 50,
            mana_back: 10,
            life_steal: 5.0,
            icon: "test_icon.png".to_string(),
            range: 10,
            dsrc: "test_dsrc".to_string(),
            reference: "test_reference".to_string(),
            target: "test_target".to_string(),
            effects: "test_effects".to_string(),
            skill_type: "test_type".to_string(),
            strl: "test_strl".to_string(),
            cooldown: 30,
            hit_targets: 3,
            pet: false,
            chance: Some(75.0),
            show_damage: true,
        };

        let result = usecase.execute(request).await;

        // Assert
        assert!(result.is_err());
        match result.unwrap_err() {
            ApplicationError::EntityAlreadyExists(name) => {
                assert_eq!(name, "Existing Skill");
            }
            _ => panic!("Expected EntityAlreadyExists error"),
        }
    }
}
