use crate::{
    error::ApplicationError,
    queries::item_rarities::{
        handler::{ItemRarityQuery<PERSON><PERSON><PERSON>, ItemRarityResponse},
        queries::GetAllItemRaritiesQuery,
    },
    traits::Query<PERSON><PERSON><PERSON>,
};

pub struct GetItemRaritiesUsecase {
    item_rarity_query_handler: ItemRarityQueryHand<PERSON>,
}

impl GetItemRaritiesUsecase {
    pub fn new(item_rarity_query_handler: ItemRarityQueryHandler) -> Self {
        Self {
            item_rarity_query_handler,
        }
    }
}

impl GetItemRaritiesUsecase {
    pub async fn execute(&self) -> Result<Vec<ItemRarityResponse>, ApplicationError> {
        let query = GetAllItemRaritiesQuery;
        let item_rarities = self.item_rarity_query_handler.handle(query).await?;

        Ok(item_rarities)
    }
}
