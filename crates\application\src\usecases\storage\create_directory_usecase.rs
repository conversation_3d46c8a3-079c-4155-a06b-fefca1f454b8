use std::sync::Arc;

use reforged_domain::traits::storage_provider::StorageProvider;

use crate::error::ApplicationError;

pub struct CreateDirectoryUsecase {
    storage_provider: Arc<dyn StorageProvider>,
}

impl CreateDirectoryUsecase {
    pub fn new(storage_provider: Arc<dyn StorageProvider>) -> Self {
        Self { storage_provider }
    }
}

impl CreateDirectoryUsecase {
    pub async fn execute(&self, path: &str) -> Result<(), ApplicationError> {
        self.storage_provider.create_directory(path).await?;

        Ok(())
    }
}
