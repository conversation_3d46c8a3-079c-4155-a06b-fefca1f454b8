use reforged_domain::{
    error::RepositoryError, models::user_purchase::entity::UserPurchase as DomainUserPurchase,
    models::user_purchase::value_object::UserPurchaseId,
    repository::user_purchase_repository::UserPurchaseRepository,
};
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{ActiveModelTrait, ModelTrait};
use sea_orm::{DatabaseConnection, EntityTrait, prelude::async_trait::async_trait};

use crate::models::user_purchases::{ActiveModel, Entity};

pub struct PostgresUserPurchaseRepository {
    db: DatabaseConnection,
}

impl PostgresUserPurchaseRepository {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

#[async_trait]
impl UserPurchaseRepository for PostgresUserPurchaseRepository {
    async fn save(&self, user_purchase: &DomainUserPurchase) -> Result<(), RepositoryError> {
        let active_model = ActiveModel {
            id: Set(user_purchase.id().get_id()),
            user_id: Set(user_purchase.user_id().get_id()),
            payment_id: Set(user_purchase.payment_id().value().clone()),
            transaction_id: Set(user_purchase.transaction_id().value().clone()),
            email: Set(user_purchase.email().value().clone()),
            hash: Set(user_purchase.hash().value().clone()),
            item: Set(user_purchase.item().value().clone()),
            item_id: Set(user_purchase.item_id().get_id()),
            price: Set(user_purchase.price().value().clone()),
            method: Set(user_purchase.method().value().clone()),
            currency: Set(user_purchase.currency().value().clone()),
            purchased: Set(user_purchase.purchased().value()),
            date: Set(user_purchase.date().value().naive_utc()),
            broadcast: Set(user_purchase.broadcast().value()),
            ..Default::default()
        };

        active_model
            .insert(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn update(&self, user_purchase: &DomainUserPurchase) -> Result<(), RepositoryError> {
        let model = Entity::find_by_id(user_purchase.id().get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?
            .ok_or_else(|| RepositoryError::NotFound("UserPurchase not found".to_string()))?;

        let mut active_model: ActiveModel = model.into();
        active_model.user_id = Set(user_purchase.user_id().get_id());
        active_model.payment_id = Set(user_purchase.payment_id().value().clone());
        active_model.transaction_id = Set(user_purchase.transaction_id().value().clone());
        active_model.email = Set(user_purchase.email().value().clone());
        active_model.hash = Set(user_purchase.hash().value().clone());
        active_model.item = Set(user_purchase.item().value().clone());
        active_model.item_id = Set(user_purchase.item_id().get_id());
        active_model.price = Set(user_purchase.price().value().clone());
        active_model.method = Set(user_purchase.method().value().clone());
        active_model.currency = Set(user_purchase.currency().value().clone());
        active_model.purchased = Set(user_purchase.purchased().value());
        active_model.date = Set(user_purchase.date().value().naive_utc());
        active_model.broadcast = Set(user_purchase.broadcast().value());

        active_model
            .update(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn delete(&self, id: &UserPurchaseId) -> Result<(), RepositoryError> {
        let model = Entity::find_by_id(id.get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?
            .ok_or_else(|| RepositoryError::NotFound("UserPurchase not found".to_string()))?;

        model
            .delete(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }
}
