use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_title::entity::UserTitle;
use reforged_domain::models::user_title::value_object::UserTitleId;
use reforged_domain::repository::user_title_repository::UserTitleReadRepository;
use reforged_shared::IdTrait;
use sea_orm::{DatabaseConnection, EntityTrait, prelude::async_trait::async_trait};

use crate::SeaORMErr;
use crate::mappers::user_title_mapper::UserTitleDbModelMapper;
use crate::models::user_titles::Entity as UserTitles;

#[allow(dead_code)]
pub struct PostgresUserTitleReadRepository {
    pool: DatabaseConnection,
}

impl PostgresUserTitleReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserTitleReadRepository for PostgresUserTitleReadRepository {
    async fn find_by_id(&self, id: &UserTitleId) -> Result<Option<UserTitle>, RepositoryError> {
        let user_title = UserTitles::find_by_id(id.get_id())
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_title {
            Some(model) => {
                let mapped = UserTitle::from(UserTitleDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<UserTitle>, RepositoryError> {
        let user_titles = UserTitles::find()
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserTitle> = user_titles
            .into_iter()
            .map(|model| UserTitle::from(UserTitleDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }
}
