use reforged_domain::{
    error::RepositoryError, models::user_report::entity::UserReport as DomainUserReport,
    models::user_report::value_object::UserReportId,
    repository::user_report_repository::UserReportRepository,
};
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{ActiveModelTrait, ModelTrait};
use sea_orm::{DatabaseConnection, EntityTrait, prelude::async_trait::async_trait};

use crate::models::user_reports::{ActiveModel, Entity};

pub struct PostgresUserReportRepository {
    db: DatabaseConnection,
}

impl PostgresUserReportRepository {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

#[async_trait]
impl UserReportRepository for PostgresUserReportRepository {
    async fn save(&self, user_report: &DomainUserReport) -> Result<(), RepositoryError> {
        let active_model = ActiveModel {
            id: Set(user_report.id().get_id()),
            user_id: Set(user_report.user_id().get_id()),
            target_name: Set(user_report.target_name().value().clone()),
            category: Set(user_report.category().value().clone()),
            description: Set(user_report.description().value().clone()),
            date_submitted: Set(user_report.date_submitted().value().naive_utc()),
            ..Default::default()
        };

        active_model
            .insert(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn update(&self, user_report: &DomainUserReport) -> Result<(), RepositoryError> {
        let model = Entity::find_by_id(user_report.id().get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?
            .ok_or_else(|| RepositoryError::NotFound("UserReport not found".to_string()))?;

        let mut active_model: ActiveModel = model.into();
        active_model.user_id = Set(user_report.user_id().get_id());
        active_model.target_name = Set(user_report.target_name().value().clone());
        active_model.category = Set(user_report.category().value().clone());
        active_model.description = Set(user_report.description().value().clone());
        active_model.date_submitted = Set(user_report.date_submitted().value().naive_utc());

        active_model
            .update(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn delete(&self, id: &UserReportId) -> Result<(), RepositoryError> {
        let model = Entity::find_by_id(id.get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?
            .ok_or_else(|| RepositoryError::NotFound("UserReport not found".to_string()))?;

        model
            .delete(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }
}
