use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_faction::entity::UserFaction;
use reforged_domain::models::user_faction::value_object::UserFactionId;
use reforged_domain::repository::user_faction_repository::UserFactionReadRepository;
use reforged_shared::IdTrait;
use sea_orm::{DatabaseConnection, EntityTrait, prelude::async_trait::async_trait};

use crate::SeaORMErr;
use crate::mappers::user_faction_mapper::UserFactionDbModelMapper;
use crate::models::user_factions::Entity as UserFactions;

#[allow(dead_code)]
pub struct PostgresUserFactionReadRepository {
    pool: DatabaseConnection,
}

impl PostgresUserFactionReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserFactionReadRepository for PostgresUserFactionReadRepository {
    async fn find_by_id(&self, id: &UserFactionId) -> Result<Option<UserFaction>, RepositoryError> {
        let user_faction = UserFactions::find_by_id(id.get_id())
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_faction {
            Some(model) => {
                let mapped = UserFaction::from(UserFactionDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<UserFaction>, RepositoryError> {
        let user_factions = UserFactions::find()
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserFaction> = user_factions
            .into_iter()
            .map(|model| UserFaction::from(UserFactionDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }
}
