use std::sync::Arc;

use reforged_domain::traits::storage_provider::StorageProvider;

use crate::error::ApplicationError;

pub struct MoveFileUsecase {
    storage_provider: Arc<dyn StorageProvider>,
}

impl MoveFileUsecase {
    pub fn new(storage_provider: Arc<dyn StorageProvider>) -> Self {
        Self { storage_provider }
    }

    pub async fn execute(&self, key: &str, new_path: &str) -> Result<(), ApplicationError> {
        self.storage_provider.move_file(key, &new_path).await?;

        Ok(())
    }
}
