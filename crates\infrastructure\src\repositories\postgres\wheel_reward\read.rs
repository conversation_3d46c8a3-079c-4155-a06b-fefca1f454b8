use reforged_domain::{
    error::RepositoryError, models::wheel_reward::entity::WheelReward as DomainWheelReward,
    models::wheel_reward::value_object::WheelRewardId,
    repository::wheel_reward_repository::WheelRewardReadRepository,
};
use reforged_shared::IdTrait;
use sea_orm::{DatabaseConnection, EntityTrait, prelude::async_trait::async_trait};

use crate::{
    mappers::wheel_reward_mapper::WheelRewardDbModelMapper, models::wheel_rewards::Entity,
};

pub struct PostgresWheelRewardReadRepository {
    db: DatabaseConnection,
}

impl PostgresWheelRewardReadRepository {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

#[async_trait]
impl WheelRewardReadRepository for PostgresWheelRewardReadRepository {
    async fn find_by_id(
        &self,
        id: &WheelRewardId,
    ) -> Result<Option<DomainWheelReward>, RepositoryError> {
        let model = Entity::find_by_id(id.get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        match model {
            Some(model) => {
                let mapper = WheelRewardDbModelMapper::new(model);
                Ok(Some(mapper.into()))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<DomainWheelReward>, RepositoryError> {
        let models = Entity::find()
            .all(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        let wheel_rewards = models
            .into_iter()
            .map(|model| {
                let mapper = WheelRewardDbModelMapper::new(model);
                mapper.into()
            })
            .collect();

        Ok(wheel_rewards)
    }
}
