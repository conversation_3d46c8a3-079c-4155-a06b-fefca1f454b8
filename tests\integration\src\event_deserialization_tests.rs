#[cfg(test)]
mod tests {
    use chrono::Utc;
    use reforged_domain::events::user_events::{
        ResetPasswordRequested, UserCreated, UserEvents, UserPasswordChanged,
    };
    use serde_json;
    use uuid::Uuid;

    #[test]
    fn test_user_created_serialization_deserialization() {
        let user_id = Uuid::now_v7();
        let user_created = UserCreated {
            id: user_id,
            username: "testuser".to_string(),
            email: "<EMAIL>".to_string(),
            role: "user".to_string(),
            gender: "other".to_string(),
            password: "hashedpassword".to_string(),
        };

        let user_event = UserEvents::UserCreated(user_created.clone());

        // Test serialization
        let serialized =
            serde_json::to_string(&user_event).expect("Failed to serialize UserCreated event");
        println!("Serialized UserCreated: {}", serialized);

        // Verify that the serialized JSON contains the event_type tag
        assert!(serialized.contains("\"event_type\":\"UserCreated\""));
        assert!(serialized.contains(&user_id.to_string()));
        assert!(serialized.contains("testuser"));
        assert!(serialized.contains("<EMAIL>"));

        // Test deserialization
        let deserialized: UserEvents =
            serde_json::from_str(&serialized).expect("Failed to deserialize UserCreated event");

        // Verify the deserialized event matches the original
        match deserialized {
            UserEvents::UserCreated(event) => {
                assert_eq!(event.id, user_created.id);
                assert_eq!(event.username, user_created.username);
                assert_eq!(event.email, user_created.email);
                assert_eq!(event.role, user_created.role);
                assert_eq!(event.gender, user_created.gender);
                assert_eq!(event.password, user_created.password);
            }
            _ => panic!("Expected UserCreated event, got {:?}", deserialized),
        }
    }

    #[test]
    fn test_reset_password_requested_serialization_deserialization() {
        let token_id = Uuid::now_v7();
        let user_id = Uuid::now_v7();
        let created_at = Utc::now();
        let expires_at = created_at + chrono::Duration::hours(1);

        let reset_password_requested = ResetPasswordRequested {
            id: token_id,
            user_id,
            email: "<EMAIL>".to_string(),
            reset_token: "reset_token_123".to_string(),
            created_at,
            expires_at,
        };

        let user_event = UserEvents::ResetPasswordRequested(reset_password_requested.clone());

        // Test serialization
        let serialized = serde_json::to_string(&user_event)
            .expect("Failed to serialize ResetPasswordRequested event");
        println!("Serialized ResetPasswordRequested: {}", serialized);

        // Verify that the serialized JSON contains the event_type tag
        assert!(serialized.contains("\"event_type\":\"ResetPasswordRequested\""));
        assert!(serialized.contains(&token_id.to_string()));
        assert!(serialized.contains(&user_id.to_string()));
        assert!(serialized.contains("<EMAIL>"));
        assert!(serialized.contains("reset_token_123"));

        // Test deserialization
        let deserialized: UserEvents = serde_json::from_str(&serialized)
            .expect("Failed to deserialize ResetPasswordRequested event");

        // Verify the deserialized event matches the original
        match deserialized {
            UserEvents::ResetPasswordRequested(event) => {
                assert_eq!(event.id, reset_password_requested.id);
                assert_eq!(event.user_id, reset_password_requested.user_id);
                assert_eq!(event.email, reset_password_requested.email);
                assert_eq!(event.reset_token, reset_password_requested.reset_token);
                assert_eq!(event.created_at, reset_password_requested.created_at);
                assert_eq!(event.expires_at, reset_password_requested.expires_at);
            }
            _ => panic!(
                "Expected ResetPasswordRequested event, got {:?}",
                deserialized
            ),
        }
    }

    #[test]
    fn test_user_password_changed_serialization_deserialization() {
        let user_id = Uuid::now_v7();
        let token_id = Uuid::now_v7();
        let password_changed_at = Utc::now();

        let user_password_changed = UserPasswordChanged {
            id: user_id,
            email: "<EMAIL>".to_string(),
            new_password: "newhashed".to_string(),
            password_changed_at,
            reset_token_id: token_id,
        };

        let user_event = UserEvents::UserPasswordChanged(user_password_changed.clone());

        // Test serialization
        let serialized = serde_json::to_string(&user_event)
            .expect("Failed to serialize UserPasswordChanged event");
        println!("Serialized UserPasswordChanged: {}", serialized);

        // Verify that the serialized JSON contains the event_type tag
        assert!(serialized.contains("\"event_type\":\"UserPasswordChanged\""));
        assert!(serialized.contains(&user_id.to_string()));
        assert!(serialized.contains(&token_id.to_string()));
        assert!(serialized.contains("<EMAIL>"));
        assert!(serialized.contains("newhashed"));

        // Test deserialization
        let deserialized: UserEvents = serde_json::from_str(&serialized)
            .expect("Failed to deserialize UserPasswordChanged event");

        // Verify the deserialized event matches the original
        match deserialized {
            UserEvents::UserPasswordChanged(event) => {
                assert_eq!(event.id, user_password_changed.id);
                assert_eq!(event.email, user_password_changed.email);
                assert_eq!(event.new_password, user_password_changed.new_password);
                assert_eq!(
                    event.password_changed_at,
                    user_password_changed.password_changed_at
                );
                assert_eq!(event.reset_token_id, user_password_changed.reset_token_id);
            }
            _ => panic!("Expected UserPasswordChanged event, got {:?}", deserialized),
        }
    }

    #[test]
    fn test_invalid_event_type_deserialization() {
        // Test that an invalid event_type fails gracefully
        let invalid_json =
            r#"{"event_type":"InvalidEvent","id":"01234567-89ab-cdef-0123-456789abcdef"}"#;

        let result = serde_json::from_str::<UserEvents>(invalid_json);
        assert!(
            result.is_err(),
            "Expected deserialization to fail for invalid event type"
        );
    }

    #[test]
    fn test_missing_event_type_deserialization() {
        // Test that missing event_type fails gracefully
        let invalid_json = r#"{"id":"01234567-89ab-cdef-0123-456789abcdef","username":"test"}"#;

        let result = serde_json::from_str::<UserEvents>(invalid_json);
        assert!(
            result.is_err(),
            "Expected deserialization to fail for missing event_type"
        );
    }

    #[test]
    fn test_malformed_json_deserialization() {
        // Test that malformed JSON fails gracefully
        let invalid_json = r#"{"event_type":"UserCreated","invalid_field":}"#;

        let result = serde_json::from_str::<UserEvents>(invalid_json);
        assert!(
            result.is_err(),
            "Expected deserialization to fail for malformed JSON"
        );
    }

    #[test]
    fn test_all_event_types_round_trip() {
        let user_id = Uuid::now_v7();
        let token_id = Uuid::now_v7();
        let now = Utc::now();

        let events = vec![
            UserEvents::UserCreated(UserCreated {
                id: user_id,
                username: "testuser".to_string(),
                email: "<EMAIL>".to_string(),
                role: "user".to_string(),
                gender: "other".to_string(),
                password: "hashedpassword".to_string(),
            }),
            UserEvents::ResetPasswordRequested(ResetPasswordRequested {
                id: token_id,
                user_id,
                email: "<EMAIL>".to_string(),
                reset_token: "reset_token_123".to_string(),
                created_at: now,
                expires_at: now + chrono::Duration::hours(1),
            }),
            UserEvents::UserPasswordChanged(UserPasswordChanged {
                id: user_id,
                email: "<EMAIL>".to_string(),
                new_password: "newhashed".to_string(),
                password_changed_at: now,
                reset_token_id: token_id,
            }),
        ];

        for (i, event) in events.iter().enumerate() {
            // Test serialization
            let serialized =
                serde_json::to_string(event).expect(&format!("Failed to serialize event {}", i));

            // Test deserialization
            let deserialized: UserEvents = serde_json::from_str(&serialized)
                .expect(&format!("Failed to deserialize event {}", i));

            // Verify round-trip consistency (simplified check)
            let reserialized = serde_json::to_string(&deserialized)
                .expect(&format!("Failed to re-serialize event {}", i));

            // The serialized forms should be equivalent
            let original_value: serde_json::Value = serde_json::from_str(&serialized).unwrap();
            let roundtrip_value: serde_json::Value = serde_json::from_str(&reserialized).unwrap();

            assert_eq!(
                original_value, roundtrip_value,
                "Round-trip failed for event {}",
                i
            );
        }
    }
}
