use actix::Actor;
use tracing::info;

use crate::actors::session::actor::SessionActorAddr;

pub type SupervisorActorAddr = actix::Addr<SupervisorActor>;

pub type SupervisorSessions = std::collections::HashMap<uuid::Uuid, SessionActorAddr>;

pub struct SupervisorActor {
    sessions: SupervisorSessions,
}

impl SupervisorActor {
    pub fn new() -> Self {
        Self {
            sessions: SupervisorSessions::new(),
        }
    }

    pub fn sessions(&self) -> &SupervisorSessions {
        &self.sessions
    }

    pub fn sessions_mut(&mut self) -> &mut SupervisorSessions {
        &mut self.sessions
    }
}

impl Actor for SupervisorActor {
    type Context = actix::Context<Self>;

    fn started(&mut self, _ctx: &mut Self::Context) {
        info!("Supervisor actor started");
    }

    fn stopped(&mut self, _ctx: &mut Self::Context) {
        info!("Supervisor actor stopped");
    }
}
