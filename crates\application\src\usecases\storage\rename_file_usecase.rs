use std::sync::Arc;

use reforged_domain::traits::storage_provider::StorageProvider;

use crate::error::ApplicationError;

pub struct RenameFileUsecase {
    storage_provider: Arc<dyn StorageProvider>,
}

impl RenameFileUsecase {
    pub fn new(storage_provider: Arc<dyn StorageProvider>) -> Self {
        Self { storage_provider }
    }
}

impl RenameFileUsecase {
    pub async fn execute(&self, old_key: &str, new_key: &str) -> Result<(), ApplicationError> {
        self.storage_provider.rename(old_key, new_key).await?;

        Ok(())
    }
}
