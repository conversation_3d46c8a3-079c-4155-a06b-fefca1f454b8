use actix::Actor;
use lapin::{
    ExchangeKind,
    options::{ExchangeDeclareOptions, QueueBindOptions, QueueDeclareOptions},
    types::{AMQPValue, FieldTable, ShortString},
};
use reforged_application::traits::BrokerServiceConsumer;
use reforged_infrastructure::{Config, broker::MessageBrokerConfig, rabbit::RabbitMQConsumer};
use reforged_infrastructure::{
    actors::message_broker::actor::MessageBrokerActor, rustls::setup_rustls,
};
use tracing::info;
use tracing_log::LogTracer;
use tracing_subscriber::{Layer, layer::SubscriberExt};

#[actix::main]
async fn main() -> anyhow::Result<()> {
    let crate_name = env!("CARGO_CRATE_NAME");
    let crate_version = env!("CARGO_PKG_VERSION");
    let name = format!("{} {}", crate_name, crate_version);

    setup_tracing();
    setup_rustls();

    let broker_config = MessageBrokerConfig::from_env()?;
    let connection =
        lapin::Connection::connect(&broker_config.url, lapin::ConnectionProperties::default())
            .await?;
    let channel = connection.create_channel().await?;

    let mut exchange_options = ExchangeDeclareOptions::default();
    exchange_options.durable = true;

    let mut args = FieldTable::default();
    args.insert(ShortString::from("x-message-ttl"), AMQPValue::LongInt(30));

    channel
        .exchange_declare(
            &broker_config.exchange,
            ExchangeKind::Fanout,
            exchange_options,
            args.clone(),
        )
        .await?;

    let mut options = QueueDeclareOptions::default();
    options.durable = true;

    let queue_name = "reforged_bus_queue2";
    let consumer_name = "reforged_emulator_consumer";

    channel.queue_declare(queue_name, options, args).await?;

    channel
        .queue_bind(
            queue_name,
            &broker_config.exchange,
            "",
            QueueBindOptions::default(),
            FieldTable::default(),
        )
        .await?;

    let message_broker = MessageBrokerActor::new(name);
    let message_broker_addr = message_broker.start();

    let rabbit_mq = RabbitMQConsumer::new(
        consumer_name.to_string(),
        queue_name.to_string(),
        channel,
        message_broker_addr,
    );
    rabbit_mq.consume().await.map_err(|e| anyhow::anyhow!(e))?;

    Ok(())
}

pub fn setup_tracing() {
    let crate_name = env!("CARGO_CRATE_NAME");
    let crate_version = env!("CARGO_PKG_VERSION");

    let filter_layer = tracing_subscriber::EnvFilter::try_from_default_env().unwrap_or_else(|_| {
        format!("RUST_LOG=info,{}=info,reforged_api=info,reforged_infrastructure=info,reforged_application=info,tokio=trace,runtime=trace,actix_web=info", crate_name).into()
    });

    let fmt_layer = tracing_subscriber::fmt::layer().with_filter(filter_layer);
    let subscriber = tracing_subscriber::registry().with(fmt_layer);

    tracing::subscriber::set_global_default(subscriber)
        .expect("Failed to set global default subscriber");

    LogTracer::init().expect("Failed to set logger");

    info!("[REFORGED] {} v{}", crate_name, crate_version);
}
