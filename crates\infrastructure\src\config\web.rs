use crate::{Config, ConfigError};

pub struct WebConfig {
    host: String,
    port: u16,
}

impl WebConfig {
    pub fn host(&self) -> String {
        self.host.clone()
    }

    pub fn port(&self) -> u16 {
        self.port
    }

    pub fn get_connection_string(&self) -> String {
        format!("{}:{}", self.host, self.port)
    }
}

impl Config for WebConfig {
    fn from_env() -> Result<Self, ConfigError> {
        dotenvy::dotenv().ok();

        let host = std::env::var("WEB_HOST")
            .map_err(|_| ConfigError::EnvVarNotFound("WEB_HOST".to_string()))?;
        let port = std::env::var("WEB_PORT")
            .map_err(|_| ConfigError::EnvVarNotFound("WEB_PORT".to_string()))?
            .parse::<u16>()
            .map_err(|_| ConfigError::EnvVarNotValid("WEB_PORT".to_string()))?;

        Ok(Self { host, port })
    }
}
