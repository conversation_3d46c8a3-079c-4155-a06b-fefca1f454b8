use std::net::Ipv4Addr;

use crate::{Config, ConfigError};

pub struct WebsocketConfig {
    host: Ipv4Addr,
    port: u16,
}

impl WebsocketConfig {
    pub fn host(&self) -> String {
        self.host.to_string()
    }

    pub fn port(&self) -> u16 {
        self.port
    }

    pub fn get_connection_string(&self) -> String {
        format!("{}:{}", self.host, self.port)
    }
}

impl Config for WebsocketConfig {
    fn from_env() -> Result<Self, ConfigError> {
        dotenvy::dotenv().ok();

        let host = std::env::var("WEBSOCKET_HOST")
            .map_err(|_| ConfigError::EnvVarNotFound("WEBSOCKET_HOST".to_string()))?
            .parse::<Ipv4Addr>()
            .map_err(|_| ConfigError::EnvVarNotValid("WEBSOCKET_HOST".to_string()))?;

        let port = std::env::var("WEBSOCKET_PORT")
            .map_err(|_| ConfigError::EnvVarNotFound("WEBSOCKET_PORT".to_string()))?
            .parse::<u16>()
            .map_err(|_| ConfigError::EnvVarNotValid("WEBSOCKET_PORT".to_string()))?;

        Ok(Self { host, port })
    }
}
