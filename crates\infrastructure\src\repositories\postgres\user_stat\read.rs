use reforged_domain::error::RepositoryError;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::user_stat::entity::UserStat;
use reforged_domain::models::user_stat::value_object::UserStatId;
use reforged_domain::repository::user_stat_repository::UserStatReadRepository;
use reforged_shared::IdTrait;
use sea_orm::{
    ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::mappers::user_stat_mapper::UserStatDbModelMapper;
use crate::models::user_stats::{Column as UserStatsColumn, Entity as UserStats};

#[allow(dead_code)]
pub struct PostgresUserStatReadRepository {
    pool: DatabaseConnection,
}

impl PostgresUserStatReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserStatReadRepository for PostgresUserStatReadRepository {
    async fn find_by_id(&self, id: &UserStatId) -> Result<Option<UserStat>, RepositoryError> {
        let user_stat = UserStats::find_by_id(id.get_id())
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_stat {
            Some(model) => {
                let mapped = UserStat::from(UserStatDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<UserStat>, RepositoryError> {
        let user_stats = UserStats::find()
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserStat> = user_stats
            .into_iter()
            .map(|model| UserStat::from(UserStatDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }

    async fn find_by_user_id(&self, user_id: &UserId) -> Result<Option<UserStat>, RepositoryError> {
        let user_stat = UserStats::find()
            .filter(UserStatsColumn::UserId.eq(user_id.get_id()))
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_stat {
            Some(model) => {
                let mapped = UserStat::from(UserStatDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }
}
