use reforged_domain::{
    error::RepositoryError, models::user::value_object::UserId,
    models::user_purchase::entity::UserPurchase as DomainUserPurchase,
    models::user_purchase::value_object::UserPurchaseId,
    repository::user_purchase_repository::UserPurchaseReadRepository,
};
use reforged_shared::IdTrait;
use sea_orm::{
    ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, prelude::async_trait::async_trait,
};

use crate::{
    mappers::user_purchase_mapper::UserPurchaseDbModelMapper,
    models::user_purchases::{Column, Entity},
};

pub struct PostgresUserPurchaseReadRepository {
    db: DatabaseConnection,
}

impl PostgresUserPurchaseReadRepository {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

#[async_trait]
impl UserPurchaseReadRepository for PostgresUserPurchaseReadRepository {
    async fn find_by_id(
        &self,
        id: &UserPurchaseId,
    ) -> Result<Option<DomainUserPurchase>, RepositoryError> {
        let model = Entity::find_by_id(id.get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        match model {
            Some(model) => {
                let mapper = UserPurchaseDbModelMapper::new(model);
                Ok(Some(mapper.into()))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<DomainUserPurchase>, RepositoryError> {
        let models = Entity::find()
            .all(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        let user_purchases = models
            .into_iter()
            .map(|model| {
                let mapper = UserPurchaseDbModelMapper::new(model);
                mapper.into()
            })
            .collect();

        Ok(user_purchases)
    }

    async fn find_by_user_id(
        &self,
        user_id: &UserId,
    ) -> Result<Vec<DomainUserPurchase>, RepositoryError> {
        let models = Entity::find()
            .filter(Column::UserId.eq(user_id.get_id()))
            .all(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        let user_purchases = models
            .into_iter()
            .map(|model| {
                let mapper = UserPurchaseDbModelMapper::new(model);
                mapper.into()
            })
            .collect();

        Ok(user_purchases)
    }

    async fn find_by_payment_id(
        &self,
        payment_id: &str,
    ) -> Result<Option<DomainUserPurchase>, RepositoryError> {
        let model = Entity::find()
            .filter(Column::PaymentId.eq(payment_id))
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        match model {
            Some(model) => {
                let mapper = UserPurchaseDbModelMapper::new(model);
                Ok(Some(mapper.into()))
            }
            None => Ok(None),
        }
    }
}
