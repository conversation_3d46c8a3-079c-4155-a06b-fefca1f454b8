use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_achievement::entity::UserAchievement;
use reforged_domain::models::user_achievement::value_object::UserAchievementId;
use reforged_domain::repository::user_achievement_repository::UserAchievementReadRepository;
use reforged_shared::IdTrait;
use sea_orm::{
    ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::mappers::user_achievement_mapper::UserAchievementDbModelMapper;
use crate::models::user_achievements::{
    Column as UserAchievementsColumn, Entity as UserAchievements,
};

#[allow(dead_code)]
pub struct PostgresUserAchievementReadRepository {
    pool: DatabaseConnection,
}

impl PostgresUserAchievementReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserAchievementReadRepository for PostgresUserAchievementReadRepository {
    async fn find_by_id(
        &self,
        id: &UserAchievementId,
    ) -> Result<Option<UserAchievement>, RepositoryError> {
        // For composite primary key tables, we need to filter by the id field
        let user_achievement = UserAchievements::find()
            .filter(UserAchievementsColumn::Id.eq(id.get_id()))
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_achievement {
            Some(model) => {
                let mapped = UserAchievement::from(UserAchievementDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<UserAchievement>, RepositoryError> {
        let user_achievements = UserAchievements::find()
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserAchievement> = user_achievements
            .into_iter()
            .map(|model| UserAchievement::from(UserAchievementDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }
}
