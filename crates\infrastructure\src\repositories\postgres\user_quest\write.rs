use num_traits::ToPrimitive;
use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_quest::entity::UserQuest;
use reforged_domain::models::user_quest::value_object::UserQuestId;
use reforged_domain::repository::user_quest_repository::UserQuestRepository;
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{
    ActiveModelTrait, DatabaseConnection, EntityTrait, IntoActiveModel,
    prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::models::user_quests::{ActiveModel as UserQuestsActiveModel, Entity as UserQuests};

#[allow(dead_code)]
pub struct PostgresUserQuestRepository {
    pool: DatabaseConnection,
}

impl PostgresUserQuestRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserQuestRepository for PostgresUserQuestRepository {
    async fn save(&self, entity: &UserQuest) -> Result<(), RepositoryError> {
        let model = UserQuestsActiveModel {
            id: Set(entity.id().get_id()),
            user_id: Set(entity.user_id().get_id()),
            quests1: Set(entity.quests1().value()),
            quests2: Set(entity.quests2().value()),
            quests3: Set(entity.quests3().value()),
            quests4: Set(entity.quests4().value()),
            quests5: Set(entity.quests5().value()),
            daily_quests0: Set(entity.daily_quests0().value().to_i32().unwrap_or_default()),
            daily_quests1: Set(entity.daily_quests1().value().to_i32().unwrap_or_default()),
            daily_quests2: Set(entity.daily_quests2().value().to_i32().unwrap_or_default()),
            monthly_quests0: Set(entity
                .monthly_quests0()
                .value()
                .to_i32()
                .unwrap_or_default()),
            daily_ads: Set(entity.daily_ads().value().to_i32().unwrap_or_default()),
        };

        model.insert(&self.pool).await.map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn update(&self, entity: &UserQuest) -> Result<(), RepositoryError> {
        let existing_model = UserQuests::find_by_id(entity.id().get_id())
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?
            .ok_or(RepositoryError::NotFound(format!(
                "UserQuest with id {}",
                entity.id().get_id()
            )))?;

        let mut active_model = existing_model.into_active_model();

        active_model.user_id = Set(entity.user_id().get_id());
        active_model.quests1 = Set(entity.quests1().value());
        active_model.quests2 = Set(entity.quests2().value());
        active_model.quests3 = Set(entity.quests3().value());
        active_model.quests4 = Set(entity.quests4().value());
        active_model.quests5 = Set(entity.quests5().value());
        active_model.daily_quests0 =
            Set(entity.daily_quests0().value().to_i32().unwrap_or_default());
        active_model.daily_quests1 =
            Set(entity.daily_quests1().value().to_i32().unwrap_or_default());
        active_model.daily_quests2 =
            Set(entity.daily_quests2().value().to_i32().unwrap_or_default());
        active_model.monthly_quests0 = Set(entity
            .monthly_quests0()
            .value()
            .to_i32()
            .unwrap_or_default());
        active_model.daily_ads = Set(entity.daily_ads().value().to_i32().unwrap_or_default());

        active_model
            .update(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn delete(&self, id: &UserQuestId) -> Result<(), RepositoryError> {
        let result = UserQuests::delete_by_id(id.get_id())
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if result.rows_affected == 0 {
            return Err(RepositoryError::NotFound(format!(
                "UserQuest with id {}",
                id.get_id()
            )));
        }

        Ok(())
    }
}
