# API Documentation
This is the API documentation for the project.

## TODO(s):
The following TODO(s) are required to be completed before creating the endpoints with relations to other tables.

### 15 Aggregate-Based Repositories to Retain

1. **UserRepository**  
   - *Scope*: User profiles, relationships, inventory, progression
   - *Entities*: `user`, `user_achievement`, `user_boost`, `user_color`, `user_currency`, `user_exp`, `user_faction`, `user_friend`, `user_guild`, `user_item`, `user_login`, `user_purchase`, `user_quest`, `user_redeem`, `user_report`, `user_slot`, `user_stat`, `user_title`, `profile`

2. **GuildRepository**  
   - *Scope*: Guild management, halls, memberships
   - *Entities*: `guild`, `guild_hall`, `guild_hall_building`, `guild_hall_connection`, `guild_inventory`, `guild_level`, `user_guild`

3. **ItemRepository**  
   - *Scope*: Items, bundles, effects
   - *Entities*: `item`, `item_bundle`, `item_effect`, `item_lottery`, `item_rarity`, `item_requirement`, `item_skill`

4. **ContentRepository**  
   - *Scope*: Game content definitions
   - *Entities*: `achievement`, `book`, `class`, `class_category`, `class_skill`, `faction`, `quest`, `skill`, `title`

5. **WorldRepository**  
   - *Scope*: Game world, maps, monsters
   - *Entities*: `map`, `map_cell`, `map_item`, `map_monster`, `monster`, `monster_boss`, `monster_drop`, `monster_skill`, `quest_location`, `quest_requirement`, `quest_reward`

6. **ShopRepository**  
   - *Scope*: Shops, items, cosmetics
   - *Entities*: `shop`, `shop_item`, `shop_location`, `hair_shop`, `hair_shop_item`, `hair`

7. **CombatRepository**  
   - *Scope*: Combat systems, enhancements
   - *Entities*: `aura`, `aura_effect`, `enhancement`, `enhancement_pattern`, `skill_aura`

8. **AuthRepository**  
   - *Scope*: Authentication, sessions
   - *Entities*: `password_reset_token`, `session`, `role`

9. **SystemRepository**  
   - *Scope*: Game settings, servers
   - *Entities*: `game_rate_setting`, `game_setting`, `profanity_filter_setting`, `server`

10. **MarketRepository**  
    - *Scope*: Player trading, rewards
    - *Entities*: `user_market`, `redeem_code`, `wheel_reward`

11. **EventRepository**  
    - *Scope*: Live events, wars
    - *Entities*: `user_livedrop`, `war`

12. **StorageRepository**  
    - *Scope*: File storage
    - *Entities*: `file`, `directory`

13. **AdminRepository**  
    - *Scope*: Admin tools
    - *Entities*: `admin_upload`, `deleted_user_item`

14. **ArticleRepository**  
    - *Scope*: CMS, news
    - *Entities*: `article`, `cms_article`

15. **BotRepository**  
    - *Scope*: Discord integration
    - *Entities*: `discord_command`, `store`

---

### Migration TODOs

#### Phase 1: Repository Consolidation
1. [ ] **Create aggregate structs**  
   - Define `UserAggregate`, `GuildAggregate`, etc. with nested entities
   - *Example*:  
     ```rust
     pub struct UserAggregate {
         user: User,
         profile: Profile,
         stats: UserStat,
         // ...
     }
     ```

2. [ ] **Implement repository traits**  
   - Create `UserRepository` trait with aggregate methods:
     ```rust
     #[async_trait]
     pub trait UserRepository {
         async fn get_aggregate(&self, id: Uuid) -> Result<UserAggregate>;
         async fn save_aggregate(&self, aggregate: &UserAggregate) -> Result<()>;
     }
     ```

3. [ ] **Migrate CRUD operations**  
   - Move table-specific methods to aggregate repositories
   - *Example*: Merge `UserItemRepository` methods into `UserRepository`

#### Phase 2: Query Optimization
4. [ ] **Implement JOIN queries**  
   - Convert N+1 queries to single queries with joins
   - *Example for UserProfile*:
     ```sql
     SELECT u.*, p.*, s.* 
     FROM users u
     LEFT JOIN profiles p ON u.id = p.user_id
     LEFT JOIN user_stats s ON u.id = s.user_id
     WHERE u.id = $1
     ```

5. [ ] **Add JSON aggregation** for 1:N relationships  
   - *Example for UserItems*:
     ```sql
     SELECT u.*, json_agg(ui.*) AS items
     FROM users u
     LEFT JOIN user_items ui ON u.id = ui.user_id
     GROUP BY u.id
     ```

6. [ ] **Implement caching layer** for frequent aggregates

#### Phase 3: Transaction Management
7. [ ] **Add transactional boundaries**  
   - Ensure atomic updates across aggregate entities:
     ```rust
     async fn update_user(&self, aggregate: UserAggregate) -> Result<()> {
         let tx = self.pool.begin().await?;
         // Update user, profile, stats in single transaction
         tx.commit().await?;
     }
     ```

8. [ ] **Handle concurrency** with optimistic locking

#### Phase 4: Deprecation & Cleanup
9. [ ] **Mark old repositories as deprecated**
   ```rust
   #[deprecated = "Use UserRepository instead"]
   pub struct UserItemRepository;
   ```

10. [ ] **Remove table-specific repositories** after full migration
11. [ ] **Update service layer** to use new repositories
12. [ ] **Benchmark performance** before/after migration
13. [ ] **Document aggregate boundaries** in architecture docs

#### Phase 5: Advanced Optimizations
14. [ ] **Implement read replicas** for aggregate queries
15. [ ] **Add materialized views** for complex aggregates
16. [ ] **Set up change data capture** for real-time updates

---

### Timeline Estimate

| Phase       | Tasks     | Estimated Time |
|-------------|-----------|----------------|
| Consolidation | 1-3       | 2-3 days       |
| Optimization | 4-6       | 1-2 days       |
| Transactions | 7-8       | 1 day          |
| Cleanup     | 9-13      | 1 day          |
| Advanced    | 14-16     | Optional       |

**Total Estimated Effort**: 5-7 developer days

### Key Metrics to Track
1. **Query Reduction**: 
   - Current: 85+ individual queries for full user load
   - Target: 1-2 aggregate queries
2. **Latency Improvement**:
   - Expect 70-90% reduction in profile load times
3. **Code Reduction**:
   - 85 → 15 repositories (~82% reduction)
4. **Error Rate**:
   - Monitor transaction failures during migration

This plan reduces complexity while improving performance through aggregate-focused design. Start with UserRepository as it will provide the most immediate benefits, then proceed to GuildRepository and ItemRepository.