use crate::{
    error::ApplicationError,
    queries::item_rarities::{
        handler::{ItemRarityQuery<PERSON><PERSON><PERSON>, ItemRarityResponse},
        queries::GetItemRarityByIdQuery,
    },
    traits::QueryHand<PERSON>,
};

pub struct GetItemRarityByIdUsecase {
    item_rarity_query_handler: ItemRarityQueryHandler,
}

impl GetItemRarityByIdUsecase {
    pub fn new(item_rarity_query_handler: ItemRarityQueryHandler) -> Self {
        Self {
            item_rarity_query_handler,
        }
    }
}

impl GetItemRarityByIdUsecase {
    pub async fn execute(&self, id: uuid::Uuid) -> Result<ItemRarityResponse, ApplicationError> {
        let query = GetItemRarityByIdQuery { id };
        let item_rarity = self.item_rarity_query_handler.handle(query).await?;

        Ok(item_rarity)
    }
}
