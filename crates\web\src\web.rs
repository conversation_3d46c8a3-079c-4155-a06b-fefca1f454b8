use actix_embed::Embed;
use actix_web::{
    App, <PERSON><PERSON><PERSON>, HttpResponse, Result,
    body::MessageBody,
    dev::{ServiceFactory, ServiceRequest, ServiceResponse},
    http::StatusCode,
    middleware::{Compress, ErrorHandlerResponse, ErrorHandlers, Logger, NormalizePath},
    web,
};
use rust_embed::RustEmbed;

#[derive(RustEmbed)]
#[folder = "../../frontend/build/client/"]
struct Assets;

pub fn create_web_service() -> App<
    impl ServiceFactory<
        ServiceRequest,
        Response = ServiceResponse<impl MessageBody>,
        Config = (),
        InitError = (),
        Error = Error,
    >,
> {
    let embed = Embed::new("/", &Assets);
    App::new()
        .wrap(NormalizePath::trim())
        .wrap(Compress::default())
        .wrap(Logger::default())
        .wrap(ErrorHandlers::new().handler(StatusCode::NOT_FOUND, error_404_error_handler))
        .route("/", web::get().to(index))
        .service(embed)
}

async fn index() -> Result<HttpResponse, Error> {
    let html = Assets::get("index.html").unwrap();
    Ok(HttpResponse::Ok().body(html.data))
}

fn error_404_error_handler<B>(res: ServiceResponse<B>) -> Result<ErrorHandlerResponse<B>> {
    let (req, _) = res.into_parts();

    let html = Assets::get("index.html").unwrap();
    let response = HttpResponse::Ok().body(html.data);

    Ok(ErrorHandlerResponse::Response(
        ServiceResponse::new(req, response).map_into_right_body(),
    ))
}
