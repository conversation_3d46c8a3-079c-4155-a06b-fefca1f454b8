use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use crate::models::{file::value_object::*, user::value_object::UserId};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, De<PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct File {
    pub id: FileId,
    pub name: FileName,
    pub full_path: FileFullPath,
    pub content_type: FileContentType,
    pub size: FileSize,
    pub uploaded_by: UserId,
    pub uploaded_at: FileUploadedAt,
    pub updated_at: FileUpdatedAt,
}

impl File {
    pub fn new(
        id: FileId,
        name: FileName,
        full_path: FileFullPath,
        content_type: FileContentType,
        size: FileSize,
        uploaded_by: UserId,
        uploaded_at: FileUploadedAt,
        updated_at: FileUpdatedAt,
    ) -> Self {
        Self {
            id,
            name,
            full_path,
            content_type,
            size,
            uploaded_by,
            uploaded_at,
            updated_at,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_file_new() {
        let id = FileId::new(uuid::Uuid::now_v7());
        let name = FileName::new("test_file.png");
        let full_path = FileFullPath::new("/path/to/test_file.png");
        let content_type = FileContentType::new("image/png");
        let size = FileSize::new(1024);
        let uploaded_by = UserId::new(uuid::Uuid::now_v7());
        let uploaded_at = FileUploadedAt::new(chrono::Utc::now());
        let updated_at = FileUpdatedAt::new(chrono::Utc::now());

        let file = File::new(
            id.clone(),
            name.clone(),
            full_path.clone(),
            content_type.clone(),
            size.clone(),
            uploaded_by.clone(),
            uploaded_at.clone(),
            updated_at.clone(),
        );

        assert_eq!(file.id.get_id(), id.get_id());
        assert_eq!(file.name.value(), name.value());
        assert_eq!(file.full_path.value(), full_path.value());
        assert_eq!(file.content_type.value(), content_type.value());
        assert_eq!(file.size.value(), size.value());
        assert_eq!(file.uploaded_by.get_id(), uploaded_by.get_id());
        assert_eq!(file.uploaded_at.value(), uploaded_at.value());
        assert_eq!(file.updated_at.value(), updated_at.value());
    }

    #[test]
    fn test_builder() {
        let id = FileId::new(uuid::Uuid::now_v7());
        let name = FileName::new("test_file.png");
        let full_path = FileFullPath::new("/path/to/test_file.png");
        let content_type = FileContentType::new("image/png");
        let size = FileSize::new(1024);
        let uploaded_by = UserId::new(uuid::Uuid::now_v7());
        let uploaded_at = FileUploadedAt::new(chrono::Utc::now());
        let updated_at = FileUpdatedAt::new(chrono::Utc::now());

        let file = File::builder()
            .id(id.clone())
            .name(name.clone())
            .full_path(full_path.clone())
            .content_type(content_type.clone())
            .size(size.clone())
            .uploaded_by(uploaded_by.clone())
            .uploaded_at(uploaded_at.clone())
            .updated_at(updated_at.clone())
            .build();

        assert_eq!(file.id.get_id(), id.get_id());
        assert_eq!(file.name.value(), name.value());
        assert_eq!(file.full_path.value(), full_path.value());
        assert_eq!(file.content_type.value(), content_type.value());
        assert_eq!(file.size.value(), size.value());
        assert_eq!(file.uploaded_by.get_id(), uploaded_by.get_id());
        assert_eq!(file.uploaded_at.value(), uploaded_at.value());
        assert_eq!(file.updated_at.value(), updated_at.value());
    }

    #[test]
    fn test_setters_getters() {
        let mut file = File::default();

        let id = FileId::new(uuid::Uuid::now_v7());
        let name = FileName::new("test_file.png");
        let full_path = FileFullPath::new("/path/to/test_file.png");
        let content_type = FileContentType::new("image/png");
        let size = FileSize::new(1024);
        let uploaded_by = UserId::new(uuid::Uuid::now_v7());
        let uploaded_at = FileUploadedAt::new(chrono::Utc::now());
        let updated_at = FileUpdatedAt::new(chrono::Utc::now());

        file.set_id(id.clone());
        file.set_name(name.clone());
        file.set_full_path(full_path.clone());
        file.set_content_type(content_type.clone());
        file.set_size(size.clone());
        file.set_uploaded_by(uploaded_by.clone());
        file.set_uploaded_at(uploaded_at.clone());
        file.set_updated_at(updated_at.clone());

        assert_eq!(file.id().get_id(), id.get_id());
        assert_eq!(file.name().value(), name.value());
        assert_eq!(file.full_path().value(), full_path.value());
        assert_eq!(file.content_type().value(), content_type.value());
        assert_eq!(file.size().value(), size.value());
        assert_eq!(file.uploaded_by().get_id(), uploaded_by.get_id());
        assert_eq!(file.uploaded_at().value(), uploaded_at.value());
        assert_eq!(file.updated_at().value(), updated_at.value());
    }
}
