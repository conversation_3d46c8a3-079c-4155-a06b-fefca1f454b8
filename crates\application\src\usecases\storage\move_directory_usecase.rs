use std::sync::Arc;

use reforged_domain::traits::storage_provider::StorageProvider;

use crate::error::ApplicationError;

pub struct MoveDirectoryUsecase {
    storage_provider: Arc<dyn StorageProvider>,
}

impl MoveDirectoryUsecase {
    pub fn new(storage_provider: Arc<dyn StorageProvider>) -> Self {
        Self { storage_provider }
    }

    pub async fn execute(&self, old_path: &str, new_path: &str) -> Result<(), ApplicationError> {
        self.storage_provider
            .rename_directory(old_path, new_path)
            .await?;

        Ok(())
    }
}
