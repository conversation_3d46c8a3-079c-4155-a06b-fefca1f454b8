use reforged_domain::{
    error::RepositoryError, models::title::entity::Title as DomainTitle,
    models::title::value_object::TitleId, repository::title_repository::TitleReadRepository,
};
use reforged_shared::IdTrait;
use sea_orm::{DatabaseConnection, EntityTrait, prelude::async_trait::async_trait};

use crate::{mappers::title_mapper::TitleDbModelMapper, models::titles::Entity};

pub struct PostgresTitleReadRepository {
    db: DatabaseConnection,
}

impl PostgresTitleReadRepository {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

#[async_trait]
impl TitleReadRepository for PostgresTitleReadRepository {
    async fn find_by_id(&self, id: &TitleId) -> Result<Option<DomainTitle>, RepositoryError> {
        let model = Entity::find_by_id(id.get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        match model {
            Some(model) => {
                let mapper = TitleDbModelMapper::new(model);
                Ok(Some(mapper.into()))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<DomainTitle>, RepositoryError> {
        let models = Entity::find()
            .all(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        let titles = models
            .into_iter()
            .map(|model| {
                let mapper = TitleDbModelMapper::new(model);
                mapper.into()
            })
            .collect();

        Ok(titles)
    }
}
