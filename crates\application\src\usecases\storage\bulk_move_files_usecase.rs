use std::sync::Arc;

use reforged_domain::traits::storage_provider::StorageProvider;

use crate::error::ApplicationError;

pub struct BulkMoveFilesUsecase {
    storage_provider: Arc<dyn StorageProvider>,
}

impl BulkMoveFilesUsecase {
    pub fn new(storage_provider: Arc<dyn StorageProvider>) -> Self {
        Self { storage_provider }
    }

    pub async fn execute(&self, files: &[(&str, &str)]) -> Result<(), ApplicationError> {
        let tasks = files
            .into_iter()
            .map(|(key, new_path)| self.storage_provider.move_file(key, new_path));

        let results = futures_util::future::join_all(tasks).await;

        for result in results {
            result?;
        }

        Ok(())
    }
}
