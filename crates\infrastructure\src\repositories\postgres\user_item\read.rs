use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_item::entity::UserItem;
use reforged_domain::models::user_item::value_object::UserItemId;
use reforged_domain::repository::user_item_repository::UserItemReadRepository;
use reforged_shared::IdTrait;
use sea_orm::{DatabaseConnection, EntityTrait, prelude::async_trait::async_trait};

use crate::SeaORMErr;
use crate::mappers::user_item_mapper::UserItemDbModelMapper;
use crate::models::user_items::Entity as UserItems;

#[allow(dead_code)]
pub struct PostgresUserItemReadRepository {
    pool: DatabaseConnection,
}

impl PostgresUserItemReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserItemReadRepository for PostgresUserItemReadRepository {
    async fn find_by_id(&self, id: &UserItemId) -> Result<Option<UserItem>, RepositoryError> {
        let user_item = UserItems::find_by_id(id.get_id())
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_item {
            Some(model) => {
                let mapped = UserItem::from(UserItemDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<UserItem>, RepositoryError> {
        let user_items = UserItems::find()
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserItem> = user_items
            .into_iter()
            .map(|model| UserItem::from(UserItemDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }
}
