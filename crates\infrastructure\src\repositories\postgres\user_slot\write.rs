use num_traits::ToPrimitive;
use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_slot::entity::UserSlot;
use reforged_domain::models::user_slot::value_object::UserSlotId;
use reforged_domain::repository::user_slot_repository::UserSlotRepository;
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{
    ActiveModelTrait, DatabaseConnection, EntityTrait, IntoActiveModel,
    prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::models::user_slots::{ActiveModel as UserSlotsActiveModel, Entity as UserSlots};

#[allow(dead_code)]
pub struct PostgresUserSlotRepository {
    pool: DatabaseConnection,
}

impl PostgresUserSlotRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserSlotRepository for PostgresUserSlotRepository {
    async fn save(&self, entity: &UserSlot) -> Result<(), RepositoryError> {
        let model = UserSlotsActiveModel {
            id: Set(entity.id().get_id()),
            user_id: Set(entity.user_id().get_id()),
            slots_bag: Set(entity.slots_bag().value().to_i16().unwrap_or_default()),
            slots_auction: Set(entity.slots_auction().value().to_i16().unwrap_or_default()),
            slots_bank: Set(entity.slots_bank().value().to_i16().unwrap_or_default()),
            slots_house: Set(entity.slots_house().value().to_i16().unwrap_or_default()),
        };

        model.insert(&self.pool).await.map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn update(&self, entity: &UserSlot) -> Result<(), RepositoryError> {
        let existing_model = UserSlots::find_by_id(entity.id().get_id())
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?
            .ok_or(RepositoryError::NotFound(format!(
                "UserSlot with id {}",
                entity.id().get_id()
            )))?;

        let mut active_model = existing_model.into_active_model();

        active_model.user_id = Set(entity.user_id().get_id());
        active_model.slots_bag = Set(entity.slots_bag().value().to_i16().unwrap_or_default());
        active_model.slots_auction =
            Set(entity.slots_auction().value().to_i16().unwrap_or_default());
        active_model.slots_bank = Set(entity.slots_bank().value().to_i16().unwrap_or_default());
        active_model.slots_house = Set(entity.slots_house().value().to_i16().unwrap_or_default());

        active_model
            .update(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn delete(&self, id: &UserSlotId) -> Result<(), RepositoryError> {
        let result = UserSlots::delete_by_id(id.get_id())
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if result.rows_affected == 0 {
            return Err(RepositoryError::NotFound(format!(
                "UserSlot with id {}",
                id.get_id()
            )));
        }

        Ok(())
    }
}
