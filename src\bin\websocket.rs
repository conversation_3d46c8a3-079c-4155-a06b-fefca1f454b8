use std::sync::Arc;

use actix::Actor;
use actix_web::{HttpServer, dev::ServerHandle};
use lapin::{
    ExchangeKind,
    options::{ExchangeDeclareOptions, QueueBindOptions, QueueDeclareOptions},
    types::{AMQPValue, FieldTable, ShortString},
};
use reforged_application::traits::BrokerServiceConsumer;
use reforged_infrastructure::{
    Config, broker::MessageBrokerConfig, paseto::PasetoAuthenticationTokenService,
    token::TokenConfig, websocket::WebsocketConfig,
};
use reforged_ws::{
    actors::supervisor::actor::SupervisorActor,
    consumer::WsRabbitMQConsumer,
    wsocket::{WsState, create_web_socket},
};
use tokio::signal;
use tracing::info;
use tracing_log::LogTracer;
use tracing_subscriber::{Layer, layer::SubscriberExt};

#[actix_web::main]
async fn main() -> anyhow::Result<()> {
    setup_tracing();
    setup_rustls();

    let broker_config = MessageBrokerConfig::from_env()?;
    let token_config = TokenConfig::from_env()?;
    let config = WebsocketConfig::from_env()?;
    let addrs = config.get_connection_string();
    let addrs_clone = addrs.clone();

    let supervisor_actor = SupervisorActor::new();
    let supervisor_addr = supervisor_actor.start();
    let token_service = PasetoAuthenticationTokenService::new(token_config.symmetric_key)
        .map_err(|e| anyhow::anyhow!("Failed to create token service: {:?}", e))?;
    let token_service = Arc::new(token_service);

    let connection =
        lapin::Connection::connect(&broker_config.url, lapin::ConnectionProperties::default())
            .await?;
    let channel = connection.create_channel().await?;

    let mut exchange_options = ExchangeDeclareOptions::default();
    exchange_options.durable = true;

    let mut args = FieldTable::default();
    args.insert(ShortString::from("x-message-ttl"), AMQPValue::LongInt(30));

    channel
        .exchange_declare(
            &broker_config.exchange,
            ExchangeKind::Fanout,
            exchange_options,
            args.clone(),
        )
        .await?;

    let mut options = QueueDeclareOptions::default();
    options.durable = true;

    let queue_name = "reforged_bus_queue3";
    let consumer_name = "reforged_ws_consumer";

    channel.queue_declare(queue_name, options, args).await?;

    channel
        .queue_bind(
            queue_name,
            &broker_config.exchange,
            "",
            QueueBindOptions::default(),
            FieldTable::default(),
        )
        .await?;

    let consumer = WsRabbitMQConsumer::new(
        consumer_name.to_string(),
        queue_name.to_string(),
        channel,
        supervisor_addr.clone(),
    );

    tokio::spawn(async move {
        consumer.consume().await.unwrap();
    });

    let server = HttpServer::new(move || {
        let supervisor_addr = supervisor_addr.clone();
        let state = WsState::new(supervisor_addr, token_service.clone());
        create_web_socket(state)
    })
    .bind(addrs)?
    .run();

    let server_handle = server.handle();

    tokio::spawn(async move {
        shutdown_signal(server_handle).await;
    });

    info!("listening on {}", addrs_clone);

    server.await?;

    Ok(())
}

pub fn setup_tracing() {
    let crate_name = env!("CARGO_CRATE_NAME");
    let crate_version = env!("CARGO_PKG_VERSION");

    let filter_layer = tracing_subscriber::EnvFilter::try_from_default_env().unwrap_or_else(|_| {
        format!("RUST_LOG=info,{}=info,reforged_api=info,reforged_infrastructure=info,reforged_application=info,reforged-ws=trace,reforged_ws=trace,tokio=trace,runtime=trace,actix_web=info", crate_name).into()
    });

    let fmt_layer = tracing_subscriber::fmt::layer().with_filter(filter_layer);
    let subscriber = tracing_subscriber::registry().with(fmt_layer);

    tracing::subscriber::set_global_default(subscriber)
        .expect("Failed to set global default subscriber");

    LogTracer::init().expect("Failed to set logger");

    info!("[REFORGED] {} v{}", crate_name, crate_version);
}

pub async fn shutdown_signal(handle: ServerHandle) {
    let ctrl_c = async {
        signal::ctrl_c()
            .await
            .expect("failed to initialize Ctrl+C handler");
    };

    #[cfg(unix)]
    let terminate = async {
        signal::unix::signal(signal::unix::SignalKind::terminate())
            .expect("failed to initialize signal handler")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();

    tokio::select! {
        _ = ctrl_c => {},
        _ = terminate => {},
    }

    info!("gracefully shutting down server...");
    info!("closing database pool connections...");
    info!("successfully closed database pool connections");
    info!("shutting down server...");
    info!("server shutdown complete");
    info!("goodbye!");

    handle.stop(true).await;
}

pub fn setup_rustls() {
    rustls::crypto::ring::default_provider()
        .install_default()
        .expect("Failed to install rustls crypto provider");
}
