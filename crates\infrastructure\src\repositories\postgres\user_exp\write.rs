use num_traits::ToPrimitive;
use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_exp::entity::UserExperience;
use reforged_domain::models::user_exp::value_object::UserExperienceId;
use reforged_domain::repository::user_exp_repository::UserExpRepository;
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{
    ActiveModelTrait, DatabaseConnection, EntityTrait, IntoActiveModel,
    prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::models::user_exps::{ActiveModel as UserExpsActiveModel, Entity as UserExps};

#[allow(dead_code)]
pub struct PostgresUserExpRepository {
    pool: DatabaseConnection,
}

impl PostgresUserExpRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserExpRepository for PostgresUserExpRepository {
    async fn save(&self, entity: &UserExperience) -> Result<(), RepositoryError> {
        let model = UserExpsActiveModel {
            id: Set(entity.id().get_id()),
            user_id: Set(entity.user_id().get_id()),
            level: Set(entity.level().value().to_i16().unwrap_or_default()),
            exp: Set(entity.experience().value().to_i64().unwrap_or_default()),
        };

        model.insert(&self.pool).await.map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn update(&self, entity: &UserExperience) -> Result<(), RepositoryError> {
        let existing_model = UserExps::find_by_id(entity.id().get_id())
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?
            .ok_or(RepositoryError::NotFound(format!(
                "UserExperience with id {}",
                entity.id().get_id()
            )))?;

        let mut active_model = existing_model.into_active_model();

        active_model.user_id = Set(entity.user_id().get_id());
        active_model.level = Set(entity.level().value().to_i16().unwrap_or_default());
        active_model.exp = Set(entity.experience().value().to_i64().unwrap_or_default());

        active_model
            .update(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn delete(&self, id: &UserExperienceId) -> Result<(), RepositoryError> {
        let result = UserExps::delete_by_id(id.get_id())
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if result.rows_affected == 0 {
            return Err(RepositoryError::NotFound(format!(
                "UserExperience with id {}",
                id.get_id()
            )));
        }

        Ok(())
    }
}
