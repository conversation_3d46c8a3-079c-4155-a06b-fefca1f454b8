use reforged_domain::error::RepositoryError;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::user_currency::entity::UserCurrency;
use reforged_domain::models::user_currency::value_object::UserCurrencyId;
use reforged_domain::repository::user_currency_repository::UserCurrencyReadRepository;
use reforged_shared::IdTrait;
use sea_orm::{
    ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::mappers::user_currency_mapper::UserCurrencyDbModelMapper;
use crate::models::user_currencies::{Column as UserCurrenciesColumn, Entity as UserCurrencies};

#[allow(dead_code)]
pub struct PostgresUserCurrencyReadRepository {
    pool: DatabaseConnection,
}

impl PostgresUserCurrencyReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserCurrencyReadRepository for PostgresUserCurrencyReadRepository {
    async fn find_by_id(
        &self,
        id: &UserCurrencyId,
    ) -> Result<Option<UserCurrency>, RepositoryError> {
        let user_currency = UserCurrencies::find_by_id(id.get_id())
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_currency {
            Some(model) => {
                let mapped = UserCurrency::from(UserCurrencyDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<UserCurrency>, RepositoryError> {
        let user_currencies = UserCurrencies::find()
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserCurrency> = user_currencies
            .into_iter()
            .map(|model| UserCurrency::from(UserCurrencyDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }

    async fn find_by_user_id(
        &self,
        user_id: &UserId,
    ) -> Result<Option<UserCurrency>, RepositoryError> {
        let user_currency = UserCurrencies::find()
            .filter(UserCurrenciesColumn::UserId.eq(user_id.get_id()))
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_currency {
            Some(model) => {
                let mapped = UserCurrency::from(UserCurrencyDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }
}
