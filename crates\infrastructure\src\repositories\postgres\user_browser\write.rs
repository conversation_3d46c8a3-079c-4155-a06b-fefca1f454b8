use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_browser::entity::UserBrowser;
use reforged_domain::models::user_browser::value_object::UserBrowserId;
use reforged_domain::repository::user_browser_repository::UserBrowserRepository;
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{
    ActiveModelTrait, DatabaseConnection, EntityTrait, IntoActiveModel,
    prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::models::user_browsers::{
    ActiveModel as UserBrowsersActiveModel, Entity as UserBrowsers,
};

#[allow(dead_code)]
pub struct PostgresUserBrowserRepository {
    pool: DatabaseConnection,
}

impl PostgresUserBrowserRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserBrowserRepository for PostgresUserBrowserRepository {
    async fn save(&self, entity: &UserBrowser) -> Result<(), RepositoryError> {
        let model = UserBrowsersActiveModel {
            id: Set(entity.id().get_id()),
            user_id: Set(entity.user_id().get_id()),
            referer: Set(entity.referer().value()),
            engine: Set(entity.engine().value()),
            platform: Set(entity.platform().value()),
            browser: Set(entity.browser().value()),
        };

        model.insert(&self.pool).await.map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn update(&self, entity: &UserBrowser) -> Result<(), RepositoryError> {
        let existing_model = UserBrowsers::find_by_id(entity.id().get_id())
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?
            .ok_or(RepositoryError::NotFound(format!(
                "UserBrowser with id {}",
                entity.id().get_id()
            )))?;

        let mut active_model = existing_model.into_active_model();

        active_model.user_id = Set(entity.user_id().get_id());
        active_model.referer = Set(entity.referer().value());
        active_model.engine = Set(entity.engine().value());
        active_model.platform = Set(entity.platform().value());
        active_model.browser = Set(entity.browser().value());

        active_model
            .update(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn delete(&self, id: &UserBrowserId) -> Result<(), RepositoryError> {
        let result = UserBrowsers::delete_by_id(id.get_id())
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if result.rows_affected == 0 {
            return Err(RepositoryError::NotFound(format!(
                "UserBrowser with id {}",
                id.get_id()
            )));
        }

        Ok(())
    }
}
