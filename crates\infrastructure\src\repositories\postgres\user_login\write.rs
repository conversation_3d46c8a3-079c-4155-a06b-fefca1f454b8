use reforged_domain::{
    error::RepositoryError, models::user_login::entity::UserLogin as DomainUserLogin,
    models::user_login::value_object::UserLoginId,
    repository::user_login_repository::UserLoginRepository,
};
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{ActiveModelTrait, ModelTrait};
use sea_orm::{DatabaseConnection, EntityTrait, prelude::async_trait::async_trait};

use crate::models::user_logins::{ActiveModel, Entity};

pub struct PostgresUserLoginRepository {
    db: DatabaseConnection,
}

impl PostgresUserLoginRepository {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

#[async_trait]
impl UserLoginRepository for PostgresUserLoginRepository {
    async fn save(&self, user_login: &DomainUserLogin) -> Result<(), RepositoryError> {
        let active_model = ActiveModel {
            id: Set(user_login.id().get_id()),
            user_id: Set(user_login.user_id().get_id()),
            location: Set(user_login.location().value().clone()),
            status: Set(user_login.status().value().clone()),
            address: Set(user_login.address().value().clone()),
            date: Set(user_login.date().value().naive_utc()),
            ..Default::default()
        };

        active_model
            .insert(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn update(&self, user_login: &DomainUserLogin) -> Result<(), RepositoryError> {
        let model = Entity::find_by_id(user_login.id().get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?
            .ok_or_else(|| RepositoryError::NotFound("UserLogin not found".to_string()))?;

        let mut active_model: ActiveModel = model.into();
        active_model.user_id = Set(user_login.user_id().get_id());
        active_model.location = Set(user_login.location().value().clone());
        active_model.status = Set(user_login.status().value().clone());
        active_model.address = Set(user_login.address().value().clone());
        active_model.date = Set(user_login.date().value().naive_utc());

        active_model
            .update(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn delete(&self, id: &UserLoginId) -> Result<(), RepositoryError> {
        let model = Entity::find_by_id(id.get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?
            .ok_or_else(|| RepositoryError::NotFound("UserLogin not found".to_string()))?;

        model
            .delete(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }
}
