use actix::{Actor<PERSON>ontex<PERSON>, <PERSON><PERSON>, Message};
use futures_util::{FutureExt, future::LocalBoxFuture};

use crate::actors::{session::actor::SessionActor, supervisor::message::RemoveSession};

#[derive(Message, Clone)]
#[rtype(result = "()")]
pub struct SetSessionId {
    id: uuid::Uuid,
}

impl SetSessionId {
    pub fn new(id: uuid::Uuid) -> Self {
        Self { id }
    }

    pub fn id(&self) -> uuid::Uuid {
        self.id
    }
}

impl Handler<SetSessionId> for SessionActor {
    type Result = ();

    fn handle(&mut self, msg: SetSessionId, _ctx: &mut Self::Context) -> Self::Result {
        self.set_id(msg.id());
    }
}

#[derive(Message, Clone)]
#[rtype(result = "()")]
pub struct Broadcast {
    message: String,
}

impl Broadcast {
    pub fn new(message: String) -> Self {
        Self { message }
    }

    pub fn message(&self) -> &str {
        &self.message
    }
}

impl Handler<Broadcast> for SessionActor {
    type Result = LocalBoxFuture<'static, ()>;

    fn handle(&mut self, msg: Broadcast, _ctx: &mut Self::Context) -> Self::Result {
        let session = self.session.clone();
        let mut session_clone = session.clone();

        let message = msg.message();
        let json = serde_json::to_string(&message).unwrap();
        tracing::info!("Sending message: {}", json);

        async move {
            if let Err(e) = session_clone.text(json).await {
                tracing::error!("Failed to send message: {}", e);
            }
        }
        .boxed_local()
    }
}

pub struct CloseSession;

impl Message for CloseSession {
    type Result = ();
}

impl Handler<CloseSession> for SessionActor {
    type Result = ();

    fn handle(&mut self, _msg: CloseSession, ctx: &mut Self::Context) -> Self::Result {
        tracing::info!("Closing session for {}", self.id());
        self.supervisor_actor.do_send(RemoveSession::new(self.id()));
        ctx.stop();
    }
}
