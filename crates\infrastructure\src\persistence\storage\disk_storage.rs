use async_trait::async_trait;
use reforged_domain::{
    models::{
        directory::{entity::Directory, value_object::DirectoryId},
        file::{
            entity::File,
            value_object::{FileId, FileUpdatedAt, FileUploadedAt},
        },
        user::value_object::UserId,
    },
    traits::storage_provider::{FileHandleTrait, StorageProvider, StorageProviderError},
};
use reforged_shared::uuid_generator::uuid_now;
use tokio::io::AsyncReadExt;
use tracing::info;

#[derive(Debug)]
pub struct DiskStorageProvider {
    root: String,
}

impl DiskStorageProvider {
    pub fn new(root: String) -> Self {
        Self { root }
    }

    pub fn root(&self) -> &str {
        &self.root
    }

    pub fn build_path(&self, path: &str) -> String {
        format!("{}/{}", self.root(), path)
    }

    pub async fn setup(&self) {
        if !tokio::fs::metadata(self.root()).await.is_ok() {
            _ = tokio::fs::create_dir_all(self.root()).await;
        }
    }
}

impl Default for DiskStorageProvider {
    fn default() -> Self {
        Self {
            root: "./storage".to_string(),
        }
    }
}

pub struct TokioFileHandle(tokio::fs::File);

#[async_trait]
impl FileHandleTrait for TokioFileHandle {
    async fn write(&mut self, buf: &[u8]) -> Result<usize, std::io::Error> {
        let file = &mut self.0;
        tokio::io::AsyncWriteExt::write(file, buf).await
    }

    async fn flush(&mut self) -> Result<(), std::io::Error> {
        let file = &mut self.0;
        tokio::io::AsyncWriteExt::flush(file).await
    }
}

#[async_trait]
impl StorageProvider for DiskStorageProvider {
    async fn get(&self, key: &str) -> Result<Vec<u8>, StorageProviderError> {
        let path = self.build_path(key);
        let mut file = tokio::fs::File::open(path)
            .await
            .map_err(|e| StorageProviderError::FileNotFound(e.to_string()))?;

        let mut contents = vec![];

        file.read_to_end(&mut contents)
            .await
            .map_err(|e| StorageProviderError::ReadError(e.to_string()))?;

        Ok(contents)
    }

    async fn get_file_handle(
        &self,
        key: &str,
    ) -> Result<Box<dyn FileHandleTrait>, StorageProviderError> {
        let key = self.build_path(key);

        let file = tokio::fs::OpenOptions::new()
            .create(false)
            .append(true)
            .open(key)
            .await
            .map_err(|e| StorageProviderError::FileNotFound(e.to_string()))?;

        Ok(Box::new(TokioFileHandle(file)))
    }

    async fn has_file(&self, key: &str) -> Result<(), StorageProviderError> {
        let path = self.build_path(key);
        let has_file = tokio::fs::metadata(&path).await.is_ok();
        if !has_file {
            return Err(StorageProviderError::FileNotFound(key.to_string()));
        }

        Ok(())
    }

    async fn create(&self, key: &str) -> Result<(), StorageProviderError> {
        let path = self.build_path(key);
        let has_file = tokio::fs::metadata(&path).await.is_ok();
        if has_file {
            return Err(StorageProviderError::AlreadyExists(key.to_string()));
        }

        tokio::fs::File::create(path)
            .await
            .map_err(|e| StorageProviderError::InvalidPath(e.to_string()))?;

        Ok(())
    }

    async fn save(
        &self,
        handle: &mut Box<dyn FileHandleTrait>,
        file: &[u8],
    ) -> Result<usize, StorageProviderError> {
        let total_bytes = handle
            .write(file)
            .await
            .map_err(|e| StorageProviderError::WriteError(e.to_string()))?;

        _ = handle.flush().await;

        Ok(total_bytes)
    }

    async fn rename(&self, old_key: &str, new_key: &str) -> Result<(), StorageProviderError> {
        let old_path = self.build_path(old_key);
        let new_path = self.build_path(new_key);

        tokio::fs::rename(old_path, new_path)
            .await
            .map_err(|e| StorageProviderError::InvalidPath(e.to_string()))?;

        Ok(())
    }

    async fn move_file(&self, key: &str, new_path: &str) -> Result<(), StorageProviderError> {
        let new_key = if new_path.eq(".") {
            let key = key.split("/").collect::<Vec<&str>>();
            key.last().unwrap().to_string()
        } else if key.contains(new_path) {
            let old_key = key.split("/").collect::<Vec<&str>>();
            let old_key = old_key.last().unwrap();
            format!("{}/{}", new_path, old_key)
        } else if new_path.split("/").collect::<Vec<&str>>().len() > 1 {
            let old_key = key.split("/").collect::<Vec<&str>>();
            let old_key = old_key.last().unwrap();
            format!("{}/{}", new_path, old_key)
        } else {
            new_path.to_string()
        };
        let new_key = new_key.as_str();

        let key = self.build_path(key);
        let new_path = self.build_path(new_key);

        info!("Moving {} to {}", key, new_path);

        tokio::fs::rename(key, new_path)
            .await
            .map_err(|e| StorageProviderError::InvalidPath(e.to_string()))?;

        Ok(())
    }

    async fn delete(&self, key: &str) -> Result<(), StorageProviderError> {
        let key = self.build_path(key);

        tokio::fs::remove_file(key)
            .await
            .map_err(|e| StorageProviderError::InvalidPath(e.to_string()))?;

        Ok(())
    }

    async fn get_directory(&self, path: &str) -> Result<Directory, StorageProviderError> {
        let path = self.build_path(path);
        let path = path.as_str();

        let mut directory = Directory::default();
        directory.set_path(path.into());

        let mut files: Vec<File> = vec![];
        let mut directories: Vec<Directory> = vec![];

        let mut entries = tokio::fs::read_dir(path)
            .await
            .map_err(|e| StorageProviderError::InvalidPath(e.to_string()))?;

        while let Some(entry) = entries
            .next_entry()
            .await
            .map_err(|e| StorageProviderError::ReadError(e.to_string()))?
        {
            let entry_type = entry
                .file_type()
                .await
                .map_err(|e| StorageProviderError::ReadError(e.to_string()))?;

            if entry_type.is_dir() {
                let file_name = entry.file_name();
                let file_name = file_name.to_string_lossy();
                let file_name = file_name.as_ref();
                let directory_path = entry.path();
                let directory_path = directory_path.to_string_lossy();
                let directory_path = directory_path.as_ref();

                directories.push(
                    Directory::builder()
                        .id(DirectoryId::new(uuid_now()))
                        .name(file_name.into())
                        .path(directory_path.into())
                        .files(vec![])
                        .directories(vec![])
                        .build(),
                );
            } else {
                let file_name = entry.file_name();
                let file_name = file_name.to_string_lossy();
                let file_name = file_name.as_ref();
                let file_path = entry.path();
                let file_path = file_path.to_string_lossy();
                let file_path = file_path.as_ref();

                let metadata = entry
                    .metadata()
                    .await
                    .map_err(|e| StorageProviderError::ReadError(e.to_string()))?;

                let size = metadata.len();

                let content_type = mime_guess::from_path(file_path);
                let content_type = content_type.first_or_octet_stream();
                let content_type = content_type.as_ref();

                let created_at = metadata.created().unwrap();
                let uploaded_at = chrono::DateTime::<chrono::Utc>::from(created_at);
                let uploaded_at = FileUploadedAt::new(uploaded_at);

                let updated_at = chrono::Utc::now();
                let updated_at = FileUpdatedAt::new(updated_at);

                files.push(
                    File::builder()
                        .id(FileId::new(uuid_now()))
                        .name(file_name.into())
                        .full_path(file_path.into())
                        .size(size.into())
                        .content_type(content_type.into())
                        .uploaded_by(UserId::new(uuid_now()))
                        .uploaded_at(uploaded_at)
                        .updated_at(updated_at)
                        .build(),
                );
            }
        }

        directory.set_files(files);
        directory.set_directories(directories);

        Ok(directory)
    }

    async fn create_directory(&self, path: &str) -> Result<(), StorageProviderError> {
        let path = self.build_path(path);
        tokio::fs::create_dir(path)
            .await
            .map_err(|e| StorageProviderError::AlreadyExists(e.to_string()))?;

        Ok(())
    }

    async fn list_directory(&self, path: &str) -> Result<Vec<Directory>, StorageProviderError> {
        let path = self.build_path(path);

        let mut directories: Vec<Directory> = vec![];
        let mut entries = tokio::fs::read_dir(path)
            .await
            .map_err(|e| StorageProviderError::InvalidPath(e.to_string()))?;

        while let Some(entry) = entries
            .next_entry()
            .await
            .map_err(|e| StorageProviderError::ReadError(e.to_string()))?
        {
            let entry_type = entry
                .file_type()
                .await
                .map_err(|e| StorageProviderError::ReadError(e.to_string()))?;

            if entry_type.is_dir() {
                let file_name = entry.file_name();
                let file_name = file_name.to_string_lossy();
                let file_name = file_name.as_ref();
                let directory_path = entry.path();
                let directory_path = directory_path.to_string_lossy();
                let directory_path = directory_path.as_ref();

                directories.push(
                    Directory::builder()
                        .id(DirectoryId::new(uuid_now()))
                        .name(file_name.into())
                        .path(directory_path.into())
                        .files(vec![])
                        .directories(vec![])
                        .build(),
                );
            }
        }

        Ok(directories)
    }

    async fn rename_directory(
        &self,
        old_path: &str,
        new_path: &str,
    ) -> Result<(), StorageProviderError> {
        let new_path = if new_path.eq(".") {
            let key = old_path.split("/").collect::<Vec<&str>>();
            key.last().unwrap().to_string()
        } else if old_path.contains(new_path) {
            let old_path = old_path.split("/").collect::<Vec<&str>>();
            let old_path = old_path.last().unwrap();
            format!("{}/{}", new_path, old_path)
        } else if new_path.split("/").collect::<Vec<&str>>().len() > 1 {
            let old_path = old_path.split("/").collect::<Vec<&str>>();
            let old_path = old_path.last().unwrap();
            format!("{}/{}", new_path, old_path)
        } else {
            new_path.to_string()
        };
        let new_path = new_path.as_str();

        let old_path = self.build_path(old_path);
        let new_path = self.build_path(new_path);

        tokio::fs::rename(old_path, new_path)
            .await
            .map_err(|e| StorageProviderError::MoveError(e.to_string()))?;

        Ok(())
    }

    async fn delete_directory(&self, path: &str) -> Result<(), StorageProviderError> {
        let path = self.build_path(path);

        tokio::fs::remove_dir_all(path)
            .await
            .map_err(|e| StorageProviderError::DeleteError(e.to_string()))?;

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use futures::StreamExt;

    use super::*;

    #[tokio::test]
    async fn test_get() {
        let storage = DiskStorageProvider::new("/test-storage".to_string());
        storage.setup().await;

        let result = storage.get("test.txt").await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_save() {
        let storage = DiskStorageProvider::new("/test-storage".to_string());
        storage.setup().await;

        _ = storage.create_directory("temp_test_files").await;
        let result = storage.create("temp_test_files/test.txt").await;
        assert!(result.is_ok());

        let mut file_handle = storage
            .get_file_handle("temp_test_files/test.txt")
            .await
            .unwrap();

        let result = storage.save(&mut file_handle, b"test").await;
        assert!(result.is_ok());

        _ = storage.delete("test.txt").await;
        _ = storage.delete_directory("temp_test_files").await;

        let result = storage.get("test.txt").await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_save_chunk() {
        let storage = DiskStorageProvider::new("/test-storage".to_string());
        storage.setup().await;

        _ = storage.create_directory("temp_test_save_chunk").await;
        _ = storage.create("temp_test_save_chunk/test.txt").await;
        let mut file_handle = storage
            .get_file_handle("temp_test_save_chunk/test.txt")
            .await
            .unwrap();

        let contents = b"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse various enim in eros elementum tristique. Sed non risus. Suspendisse lectus tortor, dignissim sit amet, adipiscing nec, ultricies sed, dolor. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse various enim in eros elementum tristique. Sed non risus. Suspendisse lectus tortor, dignissim sit amet, adipiscing nec, ultricies sed, dolor. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse various enim in eros elementum tristique. Sed non risus. Suspendisse lectus tortor, dignissim sit amet, adipiscing nec, ultricies sed, dolor. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse various enim in eros elementum tristique. Sed non risus. Suspendisse lectus tortor, dignissim sit amet, adipiscing nec, ultricies sed, dolor. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse various enim in eros elementum tristique. Sed non risus. Suspendisse lectus tortor, dignissim sit amet, adipiscing nec, ultricies sed, dolor. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse various enim in eros elementum tristique. Sed non risus. Suspendisse lectus tortor, dignissim sit amet, adipiscing nec, ultricies sed, dolor. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse various enim in eros elementum tristique. Sed non risus. Suspendisse lectus tortor, dignissim sit amet, adipiscing nec, ultricies sed, dolor. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse various enim in eros elementum tristique. Sed non risus. Suspendisse lectus tortor, dignissim sit amet, adipiscing nec, ultricies sed, dolor. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse various enim in eros elementum tristique. Sed non risus. Suspendisse lectus tortor, dignissim sit amet, adipiscing nec, ultricies sed, dolor. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse various enim in eros elementum tristique. Sed non risus. Suspendisse lectus tortor, dignissim sit amet, adipiscing nec, ultricies sed, dolor. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse various enim in eros elementum tristique. Sed non risus. Suspendisse lectus tortor, dignissim sit amet, adipiscing nec, ultricies sed, dolor. <REFORGED>";

        let file_stream = futures_util::stream::iter(contents.chunks(1024));

        let mut total_bytes_written = 0;
        let file_size = contents.len();

        tokio::pin!(file_stream);

        while let Some(chunk) = file_stream.next().await {
            let result = storage.save(&mut file_handle, chunk).await;

            assert!(result.is_ok());

            let bytes_written = result.unwrap();

            total_bytes_written += bytes_written;

            println!(
                "Progress: {} bytes of {} bytes",
                total_bytes_written, file_size
            );

            if total_bytes_written == file_size {
                break;
            }
        }

        _ = storage.delete("test.txt").await;
        _ = storage.delete_directory("temp_test_save_chunk").await;

        let result = storage.get("test.txt").await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_rename() {
        let storage = DiskStorageProvider::new("/test-storage".to_string());
        storage.setup().await;

        _ = storage.create_directory("temp_test_rename").await;
        _ = storage.create("temp_test_rename/test.txt").await;

        let mut file_handle = storage
            .get_file_handle("temp_test_rename/test.txt")
            .await
            .unwrap();

        let result = storage.save(&mut file_handle, b"test").await;
        assert!(result.is_ok());

        let result = storage
            .rename("temp_test_rename/test.txt", "temp_test_rename/test2.txt")
            .await;
        assert!(result.is_ok());

        _ = storage.delete("test2.txt").await;
        _ = storage.delete_directory("temp_test_rename").await;

        let result = storage.get("test.txt").await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_move_file() {
        let storage = DiskStorageProvider::new("/test-storage".to_string());
        storage.setup().await;

        _ = storage.create_directory("temp_test_move_file").await;
        _ = storage.create_directory("temp_test_move_file2").await;
        _ = storage.create("temp_test_move_file/test.txt").await;

        let mut file_handle = storage
            .get_file_handle("temp_test_move_file/test.txt")
            .await
            .unwrap();

        let result = storage.save(&mut file_handle, b"test").await;
        assert!(result.is_ok());

        let result = storage
            .move_file("temp_test_move_file/test.txt", "temp_test_move_file2/")
            .await;
        assert!(result.is_ok());

        _ = storage.delete("test.txt").await;
        _ = storage.delete_directory("temp_test_move_file").await;
        _ = storage.delete_directory("temp_test_move_file2").await;

        let result = storage.get("test.txt").await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_move_file_nested() {
        let storage = DiskStorageProvider::new("/test-storage".to_string());
        storage.setup().await;

        _ = storage.create_directory("temp_test_move_file_nested").await;
        _ = storage
            .create_directory("temp_test_move_file_nested/test1")
            .await;
        _ = storage
            .create_directory("temp_test_move_file_nested/test2")
            .await;
        _ = storage
            .create("temp_test_move_file_nested/test1/test.txt")
            .await;

        let mut file_handle = storage
            .get_file_handle("temp_test_move_file_nested/test1/test.txt")
            .await
            .unwrap();

        let result = storage.save(&mut file_handle, b"test").await;
        assert!(result.is_ok());

        let result = storage
            .move_file(
                "temp_test_move_file_nested/test1/test.txt",
                "temp_test_move_file_nested/test2/",
            )
            .await;
        assert!(result.is_ok());

        let result = storage
            .move_file(
                "temp_test_move_file_nested/test2/test.txt",
                "temp_test_move_file_nested/",
            )
            .await;
        assert!(result.is_ok());

        let file_content = storage.get("temp_test_move_file_nested/test.txt").await;
        assert!(file_content.is_ok());

        _ = storage.delete("test.txt").await;
        _ = storage.delete_directory("temp_test_move_file_nested").await;

        let result = storage.get("test.txt").await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_delete() {
        let storage = DiskStorageProvider::new("/test-storage".to_string());
        storage.setup().await;

        _ = storage.create_directory("temp_test_delete").await;
        _ = storage.create("temp_test_delete/test.txt").await;
        let mut file_handle = storage
            .get_file_handle("temp_test_delete/test.txt")
            .await
            .unwrap();

        let result = storage.save(&mut file_handle, b"test").await;
        assert!(result.is_ok());

        let result = storage.delete("temp_test_delete/test.txt").await;
        assert!(result.is_ok());

        _ = storage.delete_directory("temp_test_delete").await;

        let result = storage.get("test.txt").await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_get_directory() {
        let storage = DiskStorageProvider::new("/test-storage".to_string());
        storage.setup().await;

        let result = storage.get_directory("temp_test_get_directory").await;

        assert!(result.is_err());

        _ = storage.create_directory("temp_test_get_directory").await;

        let result = storage.get_directory("temp_test_get_directory").await;
        assert!(result.is_ok());

        _ = storage.delete_directory("temp_test_get_directory").await;
    }

    #[tokio::test]
    async fn test_create_directory() {
        let storage = DiskStorageProvider::new("/test-storage".to_string());
        storage.setup().await;

        let result = storage.create_directory("temp_test_create_directory").await;
        assert!(result.is_ok());

        _ = storage.delete_directory("temp_test_create_directory").await;
        let result = storage.get_directory("temp_test_create_directory").await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_list_directory() {
        let storage = DiskStorageProvider::new("/test-storage".to_string());
        storage.setup().await;

        let result = storage.list_directory("temp_test_list_directory").await;
        assert!(result.is_err());

        _ = storage.create_directory("temp_test_list_directory").await;
        _ = storage
            .create_directory("temp_test_list_directory/test1")
            .await;
        _ = storage
            .create_directory("temp_test_list_directory/test2")
            .await;
        _ = storage
            .create_directory("temp_test_list_directory/test3")
            .await;

        let result = storage.list_directory("temp_test_list_directory").await;
        assert!(result.is_ok());

        let directories = result.unwrap_or(vec![]);
        assert_eq!(directories.len(), 3);

        _ = storage.delete_directory("temp_test_list_directory").await;
    }

    #[tokio::test]
    async fn test_rename_directory() {
        let storage = DiskStorageProvider::new("/test-storage".to_string());
        storage.setup().await;

        let result = storage.create_directory("temp_test_rename_directory").await;
        assert!(result.is_ok());

        let result = storage
            .rename_directory("temp_test_rename_directory", "temp_test_rename_directory2")
            .await;
        assert!(result.is_ok());

        _ = storage
            .delete_directory("temp_test_rename_directory2")
            .await;
        let result = storage.get_directory("temp_test_rename_directory2").await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_delete_directory() {
        let storage = DiskStorageProvider::new("/test-storage".to_string());
        storage.setup().await;

        let result = storage.create_directory("temp_test_delete_directory").await;
        assert!(result.is_ok());

        let result = storage.delete_directory("temp_test_delete_directory").await;
        assert!(result.is_ok());

        let result = storage.get_directory("temp_test_delete_directory").await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_has_file() {
        let storage = DiskStorageProvider::new("/test-storage".to_string());
        storage.setup().await;

        let result = storage.has_file("test.txt").await;
        assert!(result.is_err());

        _ = storage.create("test.txt").await;
        let result = storage.has_file("test.txt").await;
        assert!(result.is_ok());

        _ = storage.delete("test.txt").await;
    }
}
