use std::sync::Arc;

use reforged_domain::{
    models::skill::value_object::SkillId,
    repository::skill_repository::SkillRepository,
};

use crate::error::ApplicationError;

pub struct DeleteSkillUsecase {
    skill_repository: Arc<dyn SkillRepository>,
}

impl DeleteSkillUsecase {
    pub fn new(skill_repository: Arc<dyn SkillRepository>) -> Self {
        Self {
            skill_repository,
        }
    }
}

impl DeleteSkillUsecase {
    pub async fn execute(&self, id: uuid::Uuid) -> Result<(), ApplicationError> {
        let id = SkillId::new(id);
        self.skill_repository.delete(&id).await?;

        Ok(())
    }
}

