use reforged_domain::{
    error::RepositoryError, models::user::value_object::UserId,
    models::user_livedrop::entity::UserLivedrop as DomainUserLivedrop,
    models::user_livedrop::value_object::UserLivedropId,
    repository::user_livedrop_repository::UserLivedropReadRepository,
};
use reforged_shared::IdTrait;
use sea_orm::{
    ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, prelude::async_trait::async_trait,
};

use crate::{
    mappers::user_livedrop_mapper::UserLivedropDbModelMapper,
    models::user_livedrops::{Column, Entity},
};

pub struct PostgresUserLivedropReadRepository {
    db: DatabaseConnection,
}

impl PostgresUserLivedropReadRepository {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

#[async_trait]
impl UserLivedropReadRepository for PostgresUserLivedropReadRepository {
    async fn find_by_id(
        &self,
        id: &UserLivedropId,
    ) -> Result<Option<DomainUserLivedrop>, RepositoryError> {
        let model = Entity::find_by_id(id.get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        match model {
            Some(model) => {
                let mapper = UserLivedropDbModelMapper::new(model);
                Ok(Some(mapper.into()))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<DomainUserLivedrop>, RepositoryError> {
        let models = Entity::find()
            .all(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        let user_livedrops = models
            .into_iter()
            .map(|model| {
                let mapper = UserLivedropDbModelMapper::new(model);
                mapper.into()
            })
            .collect();

        Ok(user_livedrops)
    }

    async fn find_by_user_id(
        &self,
        user_id: &UserId,
    ) -> Result<Vec<DomainUserLivedrop>, RepositoryError> {
        let models = Entity::find()
            .filter(Column::UserId.eq(user_id.get_id()))
            .all(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        let user_livedrops = models
            .into_iter()
            .map(|model| {
                let mapper = UserLivedropDbModelMapper::new(model);
                mapper.into()
            })
            .collect();

        Ok(user_livedrops)
    }
}
