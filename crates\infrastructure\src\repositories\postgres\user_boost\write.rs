use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_boost::entity::UserBoost;
use reforged_domain::models::user_boost::value_object::UserBoostId;
use reforged_domain::repository::user_boost_repository::UserBoostRepository;
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, DatabaseConnection, EntityTrait, IntoActiveModel, ModelTrait,
    QueryFilter, prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::models::user_boosts::{
    ActiveModel as UserBoostsActiveModel, Column as UserBoostsColumn, Entity as UserBoosts,
};

#[allow(dead_code)]
pub struct PostgresUserBoostRepository {
    pool: DatabaseConnection,
}

impl PostgresUserBoostRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserBoostRepository for PostgresUserBoostRepository {
    async fn save(&self, entity: &UserBoost) -> Result<(), RepositoryError> {
        let model = UserBoostsActiveModel {
            id: Set(entity.id().get_id()),
            user_id: Set(entity.user_id().get_id()),
            cp_boost_expire: Set(entity.cp_boost_expire().value()),
            rep_boost_expire: Set(entity.rep_boost_expire().value()),
            gold_boost_expire: Set(entity.gold_boost_expire().value()),
            exp_boost_expire: Set(entity.exp_boost_expire().value()),
            upgrade_expire: Set(entity.upgrade_expire().value()),
            upgrade_days: Set(entity.upgrade_days().value()),
            upgraded: Set(entity.upgraded().value()),
        };

        model.insert(&self.pool).await.map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn delete(&self, id: &UserBoostId) -> Result<(), RepositoryError> {
        let existing_model = UserBoosts::find()
            .filter(UserBoostsColumn::Id.eq(id.get_id()))
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?
            .ok_or(RepositoryError::NotFound(format!(
                "UserBoost with id {}",
                id.get_id()
            )))?;

        existing_model
            .delete(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn update(&self, entity: &UserBoost) -> Result<(), RepositoryError> {
        let existing_model = UserBoosts::find()
            .filter(UserBoostsColumn::Id.eq(entity.id().get_id()))
            .filter(UserBoostsColumn::UserId.eq(entity.user_id().get_id()))
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?
            .ok_or(RepositoryError::NotFound(format!(
                "UserBoost with id {} and user_id {}",
                entity.id().get_id(),
                entity.user_id().get_id()
            )))?;

        let mut active_model = existing_model.into_active_model();

        active_model.cp_boost_expire = Set(entity.cp_boost_expire().value());
        active_model.rep_boost_expire = Set(entity.rep_boost_expire().value());
        active_model.gold_boost_expire = Set(entity.gold_boost_expire().value());
        active_model.exp_boost_expire = Set(entity.exp_boost_expire().value());
        active_model.upgrade_expire = Set(entity.upgrade_expire().value());
        active_model.upgrade_days = Set(entity.upgrade_days().value());
        active_model.upgraded = Set(entity.upgraded().value());

        active_model
            .update(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }
}
