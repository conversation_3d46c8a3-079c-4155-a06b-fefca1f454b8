use async_trait::async_trait;
use reforged_domain::error::RepositoryError;
use reforged_domain::models::war::entity::War;
use reforged_domain::models::war::value_object::WarId;
use reforged_domain::repository::war_repository::WarReadRepository;
use reforged_shared::IdTrait;
use sea_orm::{DatabaseConnection, EntityTrait};

use crate::SeaORMErr;
use crate::mappers::war_mapper::WarDbModelMapper;
use crate::models::wars::Entity as Wars;

pub struct PostgresWarReadRepository {
    pool: DatabaseConnection,
}

impl PostgresWarReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl WarReadRepository for PostgresWarReadRepository {
    async fn find_by_id(&self, id: &WarId) -> Result<Option<War>, RepositoryError> {
        let war = Wars::find_by_id(id.get_id())
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(war.map(|model| War::from(WarDbModelMapper::new(model))))
    }

    async fn find_all(&self) -> Result<Vec<War>, RepositoryError> {
        let all = Wars::find()
            .all(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(all
            .into_iter()
            .map(|model| War::from(WarDbModelMapper::new(model)))
            .collect())
    }

    async fn find_active_wars(&self) -> Result<Vec<War>, RepositoryError> {
        let wars = Wars::find()
            .all(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(wars
            .into_iter()
            .map(|model| War::from(WarDbModelMapper::new(model)))
            .collect())
    }
}
