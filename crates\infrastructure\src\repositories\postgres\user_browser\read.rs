use reforged_domain::error::RepositoryError;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::user_browser::entity::UserBrowser;
use reforged_domain::models::user_browser::value_object::UserBrowserId;
use reforged_domain::repository::user_browser_repository::UserBrowserReadRepository;
use reforged_shared::IdTrait;
use sea_orm::{
    ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::mappers::user_browser_mapper::UserBrowserDbModelMapper;
use crate::models::user_browsers::{Column as UserBrowsersColumn, Entity as UserBrowsers};

#[allow(dead_code)]
pub struct PostgresUserBrowserReadRepository {
    pool: DatabaseConnection,
}

impl PostgresUserBrowserReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserBrowserReadRepository for PostgresUserBrowserReadRepository {
    async fn find_by_id(&self, id: &UserBrowserId) -> Result<Option<UserBrowser>, RepositoryError> {
        let user_browser = UserBrowsers::find_by_id(id.get_id())
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_browser {
            Some(model) => {
                let mapped = UserBrowser::from(UserBrowserDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<UserBrowser>, RepositoryError> {
        let user_browsers = UserBrowsers::find()
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserBrowser> = user_browsers
            .into_iter()
            .map(|model| UserBrowser::from(UserBrowserDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }

    async fn find_by_user_id(&self, user_id: &UserId) -> Result<Vec<UserBrowser>, RepositoryError> {
        let user_browsers = UserBrowsers::find()
            .filter(UserBrowsersColumn::UserId.eq(user_id.get_id()))
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserBrowser> = user_browsers
            .into_iter()
            .map(|model| UserBrowser::from(UserBrowserDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }
}
