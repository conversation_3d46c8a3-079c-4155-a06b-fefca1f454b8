use std::io::Read;

use actix_multipart::form::MultipartForm;
use actix_web::{
    HttpResponse, Scope,
    http::header::{HeaderN<PERSON>, HeaderValue},
    web::{self, <PERSON><PERSON>},
};
use reforged_application::usecases::storage::{
    bulk_delete_directory_usecase::BulkDeleteDirectoriesUsecase,
    bulk_delete_files_usecase::BulkDeleteFilesUsecase,
    bulk_move_directory_usecase::BulkMoveDirectories,
    bulk_move_files_usecase::BulkMoveFilesUsecase,
    bulk_upload_files_usecase::BulkUploadFilesUsecase,
    create_directory_usecase::CreateDirectoryUsecase,
    delete_directory_usecase::DeleteDirectoryUsecase,
    delete_file_usecase::DeleteFileUsecase,
    get_file_usecase::GetFileUsecase,
    get_upload_session_usecase::{GetUploadSessionResponse, GetUploadSessionUsecase},
    move_directory_usecase::MoveDirectoryUsecase,
    move_file_usecase::MoveFileUsecase,
    upload_file_usecase::UploadFileUsecase,
};
use reforged_shared::uuid_generator::uuid_now;
use tracing::info;

use crate::{
    dtos::{
        storage_dtos::{
            BulkDeleteDirectoryDTO, BulkDeleteFileDTO, BulkMoveDirectoryDTO, BulkMoveFileDTO,
            BulkUploadForm, CreateDirectoryDTO, GetUploadSessionDTO, MoveDirectoryDTO, MoveFileDTO,
            UploadForm,
        },
        validate::ValidateJson,
    },
    error::ApiError,
    middlewares::auth_middleware::Authentication,
    responses::BaseApiResponse,
    state::ApiState,
};

pub fn create_storage_service() -> Scope {
    web::scope("/storage")
        .service(
            web::resource("/files")
                .wrap(Authentication)
                .route(web::post().to(upload_file_handler)),
        )
        .service(
            web::resource("/files/bulk")
                .wrap(Authentication)
                .route(web::post().to(bulk_upload_file_handler))
                .route(web::delete().to(bulk_delete_file_handler))
                .route(web::patch().to(bulk_move_files_handler)),
        )
        .service(
            web::resource("/files/{tail:.*}")
                .route(web::get().to(get_file_handler))
                .route(web::delete().to(delete_file_handler).wrap(Authentication))
                .route(web::patch().to(move_file_handler).wrap(Authentication)),
        )
        .service(
            web::resource("/directories")
                .wrap(Authentication)
                .route(web::post().to(create_directory_handler)),
        )
        .service(
            web::resource("/directories/bulk")
                .wrap(Authentication)
                .route(web::patch().to(bulk_move_directory_handler))
                .route(web::delete().to(bulk_delete_directory_handler)),
        )
        .service(
            web::resource("/directories/{tail:.*}")
                .wrap(Authentication)
                .route(web::patch().to(move_directory_handler))
                .route(web::delete().to(delete_directory_handler)),
        )
        .service(web::resource("/session").route(web::get().to(get_upload_session_handler)))
}

const CONTENT_TYPE: HeaderName = HeaderName::from_static("content-type");
const OCTET_STREAM: HeaderValue = HeaderValue::from_static("application/octet-stream");

async fn get_file_handler(
    state: web::Data<ApiState>,
    key: web::Path<String>,
) -> Result<HttpResponse, ApiError> {
    let key = key.into_inner();
    let usecase = GetFileUsecase::new(state.storage_provider().clone());

    let file = usecase.execute(&key).await?;

    let mut response = HttpResponse::Ok().body(file);
    response.headers_mut().insert(CONTENT_TYPE, OCTET_STREAM);

    Ok(response)
}

async fn create_directory_handler(
    state: web::Data<ApiState>,
    ValidateJson(dto): ValidateJson<CreateDirectoryDTO>,
) -> Result<Json<BaseApiResponse>, ApiError> {
    let key = dto.path;
    let usecase = CreateDirectoryUsecase::new(state.storage_provider().clone());

    usecase.execute(&key).await?;

    Ok(Json(BaseApiResponse::new(format!(
        "Directory {} created",
        key
    ))))
}

async fn get_upload_session_handler(
    state: web::Data<ApiState>,
    token: web::Query<GetUploadSessionDTO>,
) -> Result<Json<GetUploadSessionResponse>, ApiError> {
    let token = token.into_inner();
    let token = token.token;
    let usecase = GetUploadSessionUsecase::new(state.token_service().clone());

    let response = usecase.execute(token).await?;

    Ok(Json(response))
}

async fn upload_file_handler(
    state: web::Data<ApiState>,
    MultipartForm(form): MultipartForm<UploadForm>,
) -> Result<Json<BaseApiResponse>, ApiError> {
    let directory = &form.json.directory.clone().unwrap_or_default();
    let token = &form.json.token;
    let uuid = uuid_now();
    let file_name = uuid.to_string();

    let file = form.file;
    let file_name = file.file_name.unwrap_or(file_name);

    let file_size = file.size;
    let mut file = file.file.as_file();
    let file = file.by_ref();

    let mut bytes = Vec::with_capacity(file_size as usize);
    let _ = file.read_to_end(&mut bytes).unwrap();

    let usecase = UploadFileUsecase::new(
        state.storage_provider().clone(),
        state.token_service().clone(),
        state.broker_service().clone(),
    );

    usecase
        .execute(token, directory, &file_name, &bytes)
        .await?;

    Ok(Json(BaseApiResponse::new(format!(
        "File {} has been uploaded successfully",
        file_name
    ))))
}

async fn bulk_upload_file_handler(
    state: web::Data<ApiState>,
    MultipartForm(form): MultipartForm<BulkUploadForm>,
) -> Result<Json<BaseApiResponse>, ApiError> {
    let directory = &form.json.directory.clone().unwrap_or_default();
    let token = &form.json.token;

    let usecase = BulkUploadFilesUsecase::new(
        state.storage_provider().clone(),
        state.token_service().clone(),
        state.broker_service().clone(),
    );

    info!("Uploading {} files", form.files.len());

    let files = form
        .files
        .iter()
        .map(|file| {
            let default_name = uuid_now().to_string();
            let file_name = file.file_name.as_ref().unwrap_or(&default_name);
            let mut bytes = Vec::with_capacity(file.size as usize);
            let _ = file.file.as_file().read_to_end(&mut bytes).unwrap();

            (file_name.to_string(), bytes)
        })
        .collect::<Vec<(String, Vec<u8>)>>();

    let files = files
        .iter()
        .map(|(name, file)| (name.as_str(), file.as_slice()))
        .collect::<Vec<(&str, &[u8])>>();

    usecase.execute(token, directory, &files).await?;

    Ok(Json(BaseApiResponse::new(
        "Files have been uploaded successfully".to_string(),
    )))
}

async fn move_file_handler(
    state: web::Data<ApiState>,
    key: web::Path<String>,
    ValidateJson(dto): ValidateJson<MoveFileDTO>,
) -> Result<Json<BaseApiResponse>, ApiError> {
    let new_path = &dto.new_path;

    let key = key.into_inner();
    let usecase = MoveFileUsecase::new(state.storage_provider().clone());

    usecase.execute(&key, new_path).await?;

    Ok(Json(BaseApiResponse::new(format!(
        "File {} has been moved successfully",
        key
    ))))
}

async fn bulk_move_files_handler(
    state: web::Data<ApiState>,
    ValidateJson(dto): ValidateJson<BulkMoveFileDTO>,
) -> Result<Json<BaseApiResponse>, ApiError> {
    let files = &dto
        .files
        .iter()
        .map(|file| (file.old_path.as_str(), file.new_path.as_str()))
        .collect::<Vec<(&str, &str)>>();

    let usecase = BulkMoveFilesUsecase::new(state.storage_provider().clone());

    usecase.execute(files).await?;

    Ok(Json(BaseApiResponse::new(
        "Files have been moved successfully".to_string(),
    )))
}

async fn delete_file_handler(
    state: web::Data<ApiState>,
    key: web::Path<String>,
) -> Result<Json<BaseApiResponse>, ApiError> {
    let key = key.into_inner();
    let usecase = DeleteFileUsecase::new(state.storage_provider().clone());

    usecase.execute(&key).await?;

    Ok(Json(BaseApiResponse::new(format!(
        "File {} has been deleted successfully",
        key
    ))))
}

async fn bulk_delete_file_handler(
    state: web::Data<ApiState>,
    ValidateJson(dto): ValidateJson<BulkDeleteFileDTO>,
) -> Result<Json<BaseApiResponse>, ApiError> {
    let files = &dto
        .files
        .iter()
        .map(|file| file.as_str())
        .collect::<Vec<&str>>();

    let usecase = BulkDeleteFilesUsecase::new(state.storage_provider().clone());

    usecase.execute(files).await?;

    Ok(Json(BaseApiResponse::new(
        "Files have been deleted successfully".to_string(),
    )))
}

async fn move_directory_handler(
    state: web::Data<ApiState>,
    key: web::Path<String>,
    ValidateJson(dto): ValidateJson<MoveDirectoryDTO>,
) -> Result<Json<BaseApiResponse>, ApiError> {
    let new_path = &dto.new_path;

    let key = key.into_inner();
    let usecase = MoveDirectoryUsecase::new(state.storage_provider().clone());

    usecase.execute(&key, new_path).await?;

    Ok(Json(BaseApiResponse::new(format!(
        "Directory {} has been moved successfully",
        key
    ))))
}

async fn bulk_move_directory_handler(
    state: web::Data<ApiState>,
    ValidateJson(dto): ValidateJson<BulkMoveDirectoryDTO>,
) -> Result<Json<BaseApiResponse>, ApiError> {
    let directories = &dto
        .directories
        .iter()
        .map(|directory| (directory.old_path.as_str(), directory.new_path.as_str()))
        .collect::<Vec<(&str, &str)>>();

    let usecase = BulkMoveDirectories::new(state.storage_provider().clone());

    usecase.execute(directories).await?;

    Ok(Json(BaseApiResponse::new(
        "Directories have been moved successfully".to_string(),
    )))
}

async fn delete_directory_handler(
    state: web::Data<ApiState>,
    key: web::Path<String>,
) -> Result<Json<BaseApiResponse>, ApiError> {
    let key = key.into_inner();
    let usecase = DeleteDirectoryUsecase::new(state.storage_provider().clone());

    usecase.execute(&key).await?;

    Ok(Json(BaseApiResponse::new(format!(
        "Directory {} has been deleted successfully",
        key
    ))))
}

async fn bulk_delete_directory_handler(
    state: web::Data<ApiState>,
    ValidateJson(dto): ValidateJson<BulkDeleteDirectoryDTO>,
) -> Result<Json<BaseApiResponse>, ApiError> {
    let directories = &dto
        .directories
        .iter()
        .map(|directory| directory.as_str())
        .collect::<Vec<&str>>();

    let usecase = BulkDeleteDirectoriesUsecase::new(state.storage_provider().clone());

    usecase.execute(directories).await?;

    Ok(Json(BaseApiResponse::new(
        "Directories have been deleted successfully".to_string(),
    )))
}
