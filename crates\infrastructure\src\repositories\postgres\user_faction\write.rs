use num_traits::ToPrimitive;
use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_faction::entity::UserFaction;
use reforged_domain::models::user_faction::value_object::UserFactionId;
use reforged_domain::repository::user_faction_repository::UserFactionRepository;
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{
    ActiveModelTrait, DatabaseConnection, EntityTrait, IntoActiveModel,
    prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::models::user_factions::{
    ActiveModel as UserFactionsActiveModel, Entity as UserFactions,
};

#[allow(dead_code)]
pub struct PostgresUserFactionRepository {
    pool: DatabaseConnection,
}

impl PostgresUserFactionRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserFactionRepository for PostgresUserFactionRepository {
    async fn save(&self, entity: &UserFaction) -> Result<(), RepositoryError> {
        let model = UserFactionsActiveModel {
            id: Set(entity.id().get_id()),
            user_id: Set(entity.user_id().get_id()),
            faction_id: Set(entity.faction_id().get_id()),
            reputation: Set(entity.reputation().value().to_i64().unwrap_or_default()),
        };

        model.insert(&self.pool).await.map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn update(&self, entity: &UserFaction) -> Result<(), RepositoryError> {
        let existing_model = UserFactions::find_by_id(entity.id().get_id())
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?
            .ok_or(RepositoryError::NotFound(format!(
                "UserFaction with id {}",
                entity.id().get_id()
            )))?;

        let mut active_model = existing_model.into_active_model();

        active_model.user_id = Set(entity.user_id().get_id());
        active_model.faction_id = Set(entity.faction_id().get_id());
        active_model.reputation = Set(entity.reputation().value().to_i64().unwrap_or_default());

        active_model
            .update(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn delete(&self, id: &UserFactionId) -> Result<(), RepositoryError> {
        let result = UserFactions::delete_by_id(id.get_id())
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if result.rows_affected == 0 {
            return Err(RepositoryError::NotFound(format!(
                "UserFaction with id {}",
                id.get_id()
            )));
        }

        Ok(())
    }
}
