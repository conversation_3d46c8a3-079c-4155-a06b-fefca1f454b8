use reforged_domain::error::RepositoryError;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::user_friend::entity::UserFriend;
use reforged_domain::models::user_friend::value_object::UserFriendId;
use reforged_domain::repository::user_friend_repository::UserFriendReadRepository;
use reforged_shared::IdTrait;
use sea_orm::{
    ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::mappers::user_friend_mapper::UserFriendDbModelMapper;
use crate::models::user_friends::{Column as UserFriendsColumn, Entity as UserFriends};

#[allow(dead_code)]
pub struct PostgresUserFriendReadRepository {
    pool: DatabaseConnection,
}

impl PostgresUserFriendReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserFriendReadRepository for PostgresUserFriendReadRepository {
    async fn find_by_id(&self, id: &UserFriendId) -> Result<Option<UserFriend>, RepositoryError> {
        let user_friend = UserFriends::find_by_id(id.get_id())
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_friend {
            Some(model) => {
                let mapped = UserFriend::from(UserFriendDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<UserFriend>, RepositoryError> {
        let user_friends = UserFriends::find()
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserFriend> = user_friends
            .into_iter()
            .map(|model| UserFriend::from(UserFriendDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }

    async fn find_by_user_id(&self, user_id: &UserId) -> Result<Vec<UserFriend>, RepositoryError> {
        let user_friends = UserFriends::find()
            .filter(UserFriendsColumn::UserId.eq(user_id.get_id()))
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserFriend> = user_friends
            .into_iter()
            .map(|model| UserFriend::from(UserFriendDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }

    async fn find_friends_of_user(
        &self,
        user_id: &UserId,
    ) -> Result<Vec<UserFriend>, RepositoryError> {
        let user_friends = UserFriends::find()
            .filter(UserFriendsColumn::UserId.eq(user_id.get_id()))
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserFriend> = user_friends
            .into_iter()
            .map(|model| UserFriend::from(UserFriendDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }
}
