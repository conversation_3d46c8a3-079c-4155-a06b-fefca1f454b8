use reforged_domain::error::RepositoryError;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::user_exp::entity::UserExperience;
use reforged_domain::models::user_exp::value_object::UserExperienceId;
use reforged_domain::repository::user_exp_repository::UserExpReadRepository;
use reforged_shared::IdTrait;
use sea_orm::{
    ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::mappers::user_exp_mapper::UserExperienceDbModelMapper;
use crate::models::user_exps::{Column as UserExpsColumn, Entity as UserExps};

#[allow(dead_code)]
pub struct PostgresUserExpReadRepository {
    pool: DatabaseConnection,
}

impl PostgresUserExpReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserExpReadRepository for PostgresUserExpReadRepository {
    async fn find_by_id(
        &self,
        id: &UserExperienceId,
    ) -> Result<Option<UserExperience>, RepositoryError> {
        let user_exp = UserExps::find_by_id(id.get_id())
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_exp {
            Some(model) => {
                let mapped = UserExperience::from(UserExperienceDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<UserExperience>, RepositoryError> {
        let user_exps = UserExps::find()
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserExperience> = user_exps
            .into_iter()
            .map(|model| UserExperience::from(UserExperienceDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }

    async fn find_by_user_id(
        &self,
        user_id: &UserId,
    ) -> Result<Option<UserExperience>, RepositoryError> {
        let user_exp = UserExps::find()
            .filter(UserExpsColumn::UserId.eq(user_id.get_id()))
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_exp {
            Some(model) => {
                let mapped = UserExperience::from(UserExperienceDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }
}
