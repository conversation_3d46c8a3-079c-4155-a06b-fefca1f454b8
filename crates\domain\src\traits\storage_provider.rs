use std::fmt::Display;

use async_trait::async_trait;

use crate::models::directory::entity::Directory;

#[async_trait]
pub trait FileHandleTrait: Send + Sync + 'static {
    async fn write(&mut self, chunk: &[u8]) -> Result<usize, std::io::Error>;
    async fn flush(&mut self) -> Result<(), std::io::Error>;
}

#[derive(Debug)]
pub enum StorageProviderError {
    FileNotFound(String),
    ReadError(String),
    WriteError(String),
    AlreadyExists(String),
    InvalidPath(String),
    ConnectionError,
    MoveError(String),
    DeleteError(String),
    InternalError,
}

impl Display for StorageProviderError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            StorageProviderError::FileNotFound(e) => write!(f, "File not found: {}", e),
            StorageProviderError::ReadError(e) => write!(f, "Read error: {}", e),
            StorageProviderError::WriteError(e) => write!(f, "Write error: {}", e),
            StorageProviderError::AlreadyExists(e) => write!(f, "File already exists: {}", e),
            StorageProviderError::InvalidPath(e) => write!(f, "Invalid path: {}", e),
            StorageProviderError::ConnectionError => write!(f, "Connection error"),
            StorageProviderError::MoveError(e) => write!(f, "Move error: {}", e),
            StorageProviderError::DeleteError(e) => write!(f, "Delete error: {}", e),
            StorageProviderError::InternalError => write!(f, "Internal error"),
        }
    }
}

#[async_trait]
#[mockall::automock]
pub trait StorageProvider: Send + Sync + 'static {
    // file operations
    async fn get(&self, key: &str) -> Result<Vec<u8>, StorageProviderError>;
    async fn get_file_handle(
        &self,
        key: &str,
    ) -> Result<Box<dyn FileHandleTrait>, StorageProviderError>;
    async fn create(&self, key: &str) -> Result<(), StorageProviderError>;
    async fn has_file(&self, key: &str) -> Result<(), StorageProviderError>;
    async fn save(
        &self,
        file: &mut Box<dyn FileHandleTrait>,
        chunk: &[u8],
    ) -> Result<usize, StorageProviderError>;
    async fn rename(&self, old_key: &str, new_key: &str) -> Result<(), StorageProviderError>;
    async fn move_file(&self, key: &str, new_path: &str) -> Result<(), StorageProviderError>;
    async fn delete(&self, key: &str) -> Result<(), StorageProviderError>;

    // directory operations
    async fn get_directory(&self, path: &str) -> Result<Directory, StorageProviderError>;
    async fn create_directory(&self, path: &str) -> Result<(), StorageProviderError>;
    async fn list_directory(&self, path: &str) -> Result<Vec<Directory>, StorageProviderError>;
    async fn rename_directory(
        &self,
        old_path: &str,
        new_path: &str,
    ) -> Result<(), StorageProviderError>;
    async fn delete_directory(&self, path: &str) -> Result<(), StorageProviderError>;
}
