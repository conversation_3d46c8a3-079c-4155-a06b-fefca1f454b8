use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_quest::entity::UserQuest;
use reforged_domain::models::user_quest::value_object::UserQuestId;
use reforged_domain::repository::user_quest_repository::UserQuestReadRepository;
use reforged_shared::IdTrait;
use sea_orm::{DatabaseConnection, EntityTrait, prelude::async_trait::async_trait};

use crate::SeaORMErr;
use crate::mappers::user_quest_mapper::UserQuestDbModelMapper;
use crate::models::user_quests::Entity as UserQuests;

#[allow(dead_code)]
pub struct PostgresUserQuestReadRepository {
    pool: DatabaseConnection,
}

impl PostgresUserQuestReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserQuestReadRepository for PostgresUserQuestReadRepository {
    async fn find_by_id(&self, id: &UserQuestId) -> Result<Option<UserQuest>, RepositoryError> {
        let user_quest = UserQuests::find_by_id(id.get_id())
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_quest {
            Some(model) => {
                let mapped = UserQuest::from(UserQuestDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<UserQuest>, RepositoryError> {
        let user_quests = UserQuests::find()
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserQuest> = user_quests
            .into_iter()
            .map(|model| UserQuest::from(UserQuestDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }
}
