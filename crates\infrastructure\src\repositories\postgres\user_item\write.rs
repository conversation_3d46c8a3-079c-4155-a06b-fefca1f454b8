use num_traits::ToPrimitive;
use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_item::entity::UserItem;
use reforged_domain::models::user_item::value_object::UserItemId;
use reforged_domain::repository::user_item_repository::UserItemRepository;
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{
    ActiveModelTrait, DatabaseConnection, EntityTrait, IntoActiveModel,
    prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::models::user_items::{ActiveModel as UserItemsActiveModel, Entity as UserItems};

#[allow(dead_code)]
pub struct PostgresUserItemRepository {
    pool: DatabaseConnection,
}

impl PostgresUserItemRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserItemRepository for PostgresUserItemRepository {
    async fn save(&self, entity: &UserItem) -> Result<(), RepositoryError> {
        let model = UserItemsActiveModel {
            id: Set(entity.id().get_id()),
            user_id: Set(entity.user_id().get_id()),
            item_id: Set(entity.item_id().get_id()),
            enh_id: Set(entity.enh_id().get_id()),
            equipped: Set(if entity.equipped().value() { 1 } else { 0 }),
            quantity: Set(entity.quantity().value().to_i64().unwrap_or_default()),
            bank: Set(if entity.bank().value() { 1 } else { 0 }),
            date_purchased: Set(entity.date_purchased().value().naive_utc()),
            bind: Set(entity.bind().value().map(|b| if b { 1i16 } else { 0i16 })),
        };

        model.insert(&self.pool).await.map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn update(&self, entity: &UserItem) -> Result<(), RepositoryError> {
        let existing_model = UserItems::find_by_id(entity.id().get_id())
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?
            .ok_or(RepositoryError::NotFound(format!(
                "UserItem with id {}",
                entity.id().get_id()
            )))?;

        let mut active_model = existing_model.into_active_model();

        active_model.user_id = Set(entity.user_id().get_id());
        active_model.item_id = Set(entity.item_id().get_id());
        active_model.enh_id = Set(entity.enh_id().get_id());
        active_model.equipped = Set(if entity.equipped().value() { 1 } else { 0 });
        active_model.quantity = Set(entity.quantity().value().to_i64().unwrap_or_default());
        active_model.bank = Set(if entity.bank().value() { 1 } else { 0 });
        active_model.date_purchased = Set(entity.date_purchased().value().naive_utc());
        active_model.bind = Set(entity.bind().value().map(|b| if b { 1i16 } else { 0i16 }));

        active_model
            .update(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn delete(&self, id: &UserItemId) -> Result<(), RepositoryError> {
        let result = UserItems::delete_by_id(id.get_id())
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if result.rows_affected == 0 {
            return Err(RepositoryError::NotFound(format!(
                "UserItem with id {}",
                id.get_id()
            )));
        }

        Ok(())
    }
}
