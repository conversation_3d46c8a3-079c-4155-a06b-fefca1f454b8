use std::sync::Arc;

use reforged_domain::traits::storage_provider::StorageProvider;

use crate::error::ApplicationError;

pub struct DeleteFileUsecase {
    storage_provider: Arc<dyn StorageProvider>,
}

impl DeleteFileUsecase {
    pub fn new(storage_provider: Arc<dyn StorageProvider>) -> Self {
        Self { storage_provider }
    }

    pub async fn execute(&self, key: &str) -> Result<(), ApplicationError> {
        self.storage_provider.delete(key).await?;

        Ok(())
    }
}
