use num_traits::ToPrimitive;
use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_stat::entity::UserStat;
use reforged_domain::models::user_stat::value_object::UserStatId;
use reforged_domain::repository::user_stat_repository::UserStatRepository;
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{
    ActiveModelTrait, DatabaseConnection, EntityTrait, IntoActiveModel,
    prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::models::user_stats::{ActiveModel as UserStatsActiveModel, Entity as UserStats};

#[allow(dead_code)]
pub struct PostgresUserStatRepository {
    pool: DatabaseConnection,
}

impl PostgresUserStatRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserStatRepository for PostgresUserStatRepository {
    async fn save(&self, entity: &UserStat) -> Result<(), RepositoryError> {
        let model = UserStatsActiveModel {
            id: Set(entity.id().get_id()),
            user_id: Set(entity.user_id().get_id()),
            last_area: Set(entity.last_area().value().clone()),
            current_server: Set(entity.current_server().value().clone()),
            house_info: Set(entity.house_info().value().clone()),
            kill_count: Set(entity.kill_count().value().to_i64().unwrap_or_default()),
            death_count: Set(entity.death_count().value().to_i64().unwrap_or_default()),
            pvp_ratio: Set(entity
                .pvp_ratio()
                .value()
                .map(|v| v.to_i64().unwrap_or_default())),
        };

        model.insert(&self.pool).await.map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn update(&self, entity: &UserStat) -> Result<(), RepositoryError> {
        let existing_model = UserStats::find_by_id(entity.id().get_id())
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?
            .ok_or(RepositoryError::NotFound(format!(
                "UserStat with id {}",
                entity.id().get_id()
            )))?;

        let mut active_model = existing_model.into_active_model();

        active_model.user_id = Set(entity.user_id().get_id());
        active_model.last_area = Set(entity.last_area().value().clone());
        active_model.current_server = Set(entity.current_server().value().clone());
        active_model.house_info = Set(entity.house_info().value().clone());
        active_model.kill_count = Set(entity.kill_count().value().to_i64().unwrap_or_default());
        active_model.death_count = Set(entity.death_count().value().to_i64().unwrap_or_default());
        active_model.pvp_ratio = Set(entity
            .pvp_ratio()
            .value()
            .map(|v| v.to_i64().unwrap_or_default()));

        active_model
            .update(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn delete(&self, id: &UserStatId) -> Result<(), RepositoryError> {
        let result = UserStats::delete_by_id(id.get_id())
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if result.rows_affected == 0 {
            return Err(RepositoryError::NotFound(format!(
                "UserStat with id {}",
                id.get_id()
            )));
        }

        Ok(())
    }
}
