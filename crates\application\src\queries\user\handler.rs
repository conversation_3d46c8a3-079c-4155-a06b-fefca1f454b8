use reforged_shared::{IdTrait, Value, uuid_generator::uuid_to_u64};
use serde::Serialize;
use std::sync::Arc;

use async_trait::async_trait;
use reforged_domain::{
    models::{
        achievement::entity::Achievement,
        faction::entity::Faction,
        guild::entity::Guild,
        profile::entity::Profile,
        title::entity::Title,
        user::{
            entity::User,
            profile::UserProfile,
            value_object::{Email, HashedPassword, UserId, UserRole, Username},
        },
        user_boost::entity::UserBoost,
        user_color::entity::UserColor,
        user_currency::entity::UserCurrency,
        user_exp::entity::UserExperience,
        user_item::entity::UserItem,
        user_stat::entity::UserStat,
    },
    repository::user_repository::UserReadRepository,
};

use crate::{queries::user::queries::GetUserProfileQuery, traits::QueryHandler};

use super::queries::{
    GetLatestIdQuery, GetUserByEmailQuery, GetUserByIdQuery, GetUserByUsernameQuery, ListUsersQuery,
};

#[derive(Clone, bon::Builder)]
pub struct UserQueryHandler {
    user_repo: Arc<dyn UserReadRepository>,
}

#[async_trait]
impl QueryHandler<GetUserByEmailQuery> for UserQueryHandler {
    type Output = Option<UserResponse>;

    async fn handle(
        &self,
        query: GetUserByEmailQuery,
    ) -> Result<Option<UserResponse>, crate::error::ApplicationError> {
        let user_repo = self.user_repo.clone();
        let user = user_repo
            .find_by_email(&query.email)
            .await?
            .map(UserResponse::from);

        Ok(user)
    }
}

#[async_trait]
impl QueryHandler<GetUserByIdQuery> for UserQueryHandler {
    type Output = Option<UserResponse>;

    async fn handle(
        &self,
        query: GetUserByIdQuery,
    ) -> Result<Option<UserResponse>, crate::error::ApplicationError> {
        let id = UserId::new(query.id());
        let user_repo = self.user_repo.clone();
        let user = user_repo.find_by_id(&id).await?.map(UserResponse::from);

        Ok(user)
    }
}

#[async_trait]
impl QueryHandler<ListUsersQuery> for UserQueryHandler {
    type Output = ();

    async fn handle(&self, _query: ListUsersQuery) -> Result<(), crate::error::ApplicationError> {
        Ok(())
    }
}

#[async_trait]
impl QueryHandler<GetUserByUsernameQuery> for UserQueryHandler {
    type Output = Option<UserResponse>;

    async fn handle(
        &self,
        query: GetUserByUsernameQuery,
    ) -> Result<Option<UserResponse>, crate::error::ApplicationError> {
        let user_repo = self.user_repo.clone();
        let user = user_repo
            .find_by_username(&query.username)
            .await?
            .map(UserResponse::from);

        Ok(user)
    }
}

#[async_trait]
impl QueryHandler<GetLatestIdQuery> for UserQueryHandler {
    type Output = UserId;

    async fn handle(
        &self,
        _query: GetLatestIdQuery,
    ) -> Result<UserId, crate::error::ApplicationError> {
        let user_repo = self.user_repo.clone();
        let user_id = user_repo.get_latest_id().await.unwrap_or_default();

        Ok(user_id)
    }
}

#[async_trait]
impl QueryHandler<GetUserProfileQuery> for UserQueryHandler {
    type Output = Option<UserProfileResponse>;

    async fn handle(
        &self,
        query: GetUserProfileQuery,
    ) -> Result<Option<UserProfileResponse>, crate::error::ApplicationError> {
        let id = UserId::new(query.id);
        let user_repo = self.user_repo.clone();
        let user_profile = user_repo
            .get_with_profile(&id)
            .await?
            .map(UserProfileResponse::from);

        Ok(user_profile)
    }
}

#[derive(Serialize, bon::Builder)]
pub struct UserResponse {
    pub id: uuid::Uuid,
    pub pid: u64,
    pub username: String,
    #[serde(skip)]
    pub email: String,
    pub gender: String,
    pub role: String,
    #[serde(skip)]
    pub hashed_password: String,
    #[serde(skip)]
    pub salt: String,
}

impl From<(User, Option<Profile>)> for UserResponse {
    fn from(value: (User, Option<Profile>)) -> Self {
        let (user, profile) = value;
        let gender = profile
            .map(|profile| profile.gender().to_string())
            .unwrap_or_default();
        let pid = uuid_to_u64(&user.id().get_id());

        Self::builder()
            .id(user.id().get_id())
            .pid(pid)
            .username(user.username().value())
            .email(user.email().value())
            .gender(gender)
            .role(user.role().to_string()) // TODO: wrong mapping of role
            .hashed_password(user.hashed_password().hash().to_string())
            .salt(user.hashed_password().salt().to_string())
            .build()
    }
}

impl From<UserResponse> for User {
    fn from(value: UserResponse) -> Self {
        let UserResponse {
            id,
            pid: _,
            username,
            email,
            gender,
            role,
            hashed_password,
            salt,
        } = value;

        _ = gender;
        let role = UserRole::try_from(role).unwrap_or_default();

        User::builder()
            .id(UserId::new(id))
            .username(Username::new(username))
            .email(Email::new(email))
            .role(role)
            .hashed_password(HashedPassword::new(hashed_password, salt))
            .build()
    }
}

// ===== UserProfile Response Structs =====

#[derive(Serialize, bon::Builder)]
pub struct UserProfileResponse {
    pub user: UserResponse,
    pub info: ProfileResponse,
    pub color: UserColorResponse,
    pub titles: Vec<TitleResponse>,
    pub stats: UserStatResponse,
    pub exp: UserExperienceResponse,
    pub achievements: Vec<AchievementResponse>,
    pub friends: Vec<UserResponse>,
    pub guild: Option<GuildResponse>,
    pub factions: Vec<FactionResponse>,
    pub currencies: UserCurrencyResponse,
    pub equipment: Vec<UserItemResponse>,
    pub boosts: UserBoostResponse,
}

#[derive(Serialize, bon::Builder)]
pub struct ProfileResponse {
    pub id: uuid::Uuid,
    pub user_id: uuid::Uuid,
    pub country: String,
    pub age: u16,
    pub gender: String,
    pub avatar: String,
}

#[derive(Serialize, bon::Builder)]
pub struct UserColorResponse {
    pub id: uuid::Uuid,
    pub user_id: uuid::Uuid,
    pub color_chat: String,
    pub color_name: String,
    pub color_hair: String,
    pub color_skin: String,
    pub color_eye: String,
    pub color_base: String,
    pub color_trim: String,
    pub color_accessory: String,
}

#[derive(Serialize, bon::Builder)]
pub struct TitleResponse {
    pub id: uuid::Uuid,
    pub name: String,
    pub description: String,
    pub color: String,
    pub strength: i32,
    pub intellect: i32,
    pub endurance: i32,
    pub dexterity: i32,
    pub wisdom: i32,
    pub luck: i32,
    pub role_id: uuid::Uuid,
}

#[derive(Serialize, bon::Builder)]
pub struct UserStatResponse {
    pub id: uuid::Uuid,
    pub user_id: uuid::Uuid,
    pub last_area: String,
    pub current_server: String,
    pub house_info: String,
    pub kill_count: i64,
    pub death_count: i64,
    pub pvp_ratio: Option<i64>,
}

#[derive(Serialize, bon::Builder)]
pub struct UserExperienceResponse {
    pub id: uuid::Uuid,
    pub user_id: uuid::Uuid,
    pub experience: u32,
    pub level: u16,
}

#[derive(Serialize, bon::Builder)]
pub struct AchievementResponse {
    pub id: uuid::Uuid,
    pub name: String,
    pub description: String,
    pub file: String,
    pub category: String,
    pub show: bool,
}

#[derive(Serialize, bon::Builder)]
pub struct GuildResponse {
    pub id: uuid::Uuid,
    pub name: String,
    pub message_of_the_day: String,
    pub max_members: i16,
    pub hall_size: i16,
    pub last_updated: chrono::DateTime<chrono::Utc>,
    pub wins: i32,
    pub loss: i32,
    pub total_kills: i32,
    pub level: i32,
    pub experience: i64,
    pub guild_color: String,
    pub staff_g: uuid::Uuid,
    pub color: Option<i32>,
}

#[derive(Serialize, bon::Builder)]
pub struct FactionResponse {
    pub id: uuid::Uuid,
    pub name: String,
}

#[derive(Serialize, bon::Builder)]
pub struct UserCurrencyResponse {
    pub id: uuid::Uuid,
    pub user_id: uuid::Uuid,
    pub gold: i64,
    pub coins: i64,
    pub diamonds: i64,
    pub crystal: i64,
}

#[derive(Serialize, bon::Builder)]
pub struct UserItemResponse {
    pub id: uuid::Uuid,
    pub user_id: uuid::Uuid,
    pub item_id: uuid::Uuid,
    pub enh_id: uuid::Uuid,
    pub equipped: bool,
    pub quantity: u64,
    pub bank: bool,
    pub bind: Option<bool>,
    pub date_purchased: chrono::DateTime<chrono::Utc>,
}

#[derive(Serialize, bon::Builder)]
pub struct UserBoostResponse {
    pub id: uuid::Uuid,
    pub user_id: uuid::Uuid,
    pub cp_boost_expire: chrono::NaiveDate,
    pub rep_boost_expire: chrono::NaiveDate,
    pub gold_boost_expire: chrono::NaiveDate,
    pub exp_boost_expire: chrono::NaiveDate,
    pub upgrade_expire: chrono::NaiveDate,
    pub upgrade_days: i16,
    pub upgraded: bool,
}

// ===== From Trait Implementations =====

impl From<UserProfile> for UserProfileResponse {
    fn from(profile: UserProfile) -> Self {
        Self::builder()
            .user(UserResponse::from((
                profile.user().clone(),
                Some(profile.info().clone()),
            )))
            .info(ProfileResponse::from(profile.info().clone()))
            .color(UserColorResponse::from(profile.color().clone()))
            .titles(
                profile
                    .titles()
                    .iter()
                    .map(|t| TitleResponse::from(t.clone()))
                    .collect(),
            )
            .stats(UserStatResponse::from(profile.stats().clone()))
            .exp(UserExperienceResponse::from(profile.exp().clone()))
            .achievements(
                profile
                    .achievements()
                    .iter()
                    .map(|a| AchievementResponse::from(a.clone()))
                    .collect(),
            )
            .friends(
                profile
                    .friends()
                    .iter()
                    .map(|f| UserResponse::from((f.clone(), None)))
                    .collect(),
            )
            .maybe_guild(
                profile
                    .guild()
                    .as_ref()
                    .map(|g| GuildResponse::from(g.clone())),
            )
            .factions(
                profile
                    .factions()
                    .iter()
                    .map(|f| FactionResponse::from(f.clone()))
                    .collect(),
            )
            .currencies(UserCurrencyResponse::from(profile.currencies().clone()))
            .equipment(
                profile
                    .equipment()
                    .iter()
                    .map(|e| UserItemResponse::from(e.clone()))
                    .collect(),
            )
            .boosts(UserBoostResponse::from(profile.boosts().clone()))
            .build()
    }
}

impl From<Profile> for ProfileResponse {
    fn from(profile: Profile) -> Self {
        Self::builder()
            .id(profile.id().get_id())
            .user_id(profile.user_id().get_id())
            .country(profile.country().value())
            .age(profile.age().value())
            .gender(profile.gender().to_string())
            .avatar(profile.avatar().value())
            .build()
    }
}

impl From<UserColor> for UserColorResponse {
    fn from(color: UserColor) -> Self {
        Self::builder()
            .id(color.id().get_id())
            .user_id(color.user_id().get_id())
            .color_chat(color.color_chat().value())
            .color_name(color.color_name().value())
            .color_hair(color.color_hair().value())
            .color_skin(color.color_skin().value())
            .color_eye(color.color_eye().value())
            .color_base(color.color_base().value())
            .color_trim(color.color_trim().value())
            .color_accessory(color.color_accessory().value())
            .build()
    }
}

impl From<Title> for TitleResponse {
    fn from(title: Title) -> Self {
        Self::builder()
            .id(title.id().get_id())
            .name(title.name().value())
            .description(title.description().value())
            .color(title.color().value())
            .strength(title.strength().value())
            .intellect(title.intellect().value())
            .endurance(title.endurance().value())
            .dexterity(title.dexterity().value())
            .wisdom(title.wisdom().value())
            .luck(title.luck().value())
            .role_id(title.role_id().get_id())
            .build()
    }
}

impl From<UserStat> for UserStatResponse {
    fn from(stat: UserStat) -> Self {
        Self::builder()
            .id(stat.id().get_id())
            .user_id(stat.user_id().get_id())
            .last_area(stat.last_area().value())
            .current_server(stat.current_server().value())
            .house_info(stat.house_info().value())
            .kill_count(stat.kill_count().value())
            .death_count(stat.death_count().value())
            .pvp_ratio(stat.pvp_ratio().value().unwrap_or(0))
            .build()
    }
}

impl From<UserExperience> for UserExperienceResponse {
    fn from(exp: UserExperience) -> Self {
        Self::builder()
            .id(exp.id().get_id())
            .user_id(exp.user_id().get_id())
            .experience(exp.experience().value())
            .level(exp.level().value())
            .build()
    }
}

impl From<Achievement> for AchievementResponse {
    fn from(achievement: Achievement) -> Self {
        Self::builder()
            .id(achievement.id().get_id())
            .name(achievement.name().value())
            .description(achievement.description().value())
            .file(achievement.file().value())
            .category(achievement.category().value())
            .show(achievement.show().value())
            .build()
    }
}

impl From<Guild> for GuildResponse {
    fn from(guild: Guild) -> Self {
        Self::builder()
            .id(guild.id().get_id())
            .name(guild.name().value())
            .message_of_the_day(guild.message_of_the_day().value())
            .max_members(guild.max_members().value())
            .hall_size(guild.hall_size().value())
            .last_updated(guild.last_updated().value())
            .wins(guild.wins().value())
            .loss(guild.loss().value())
            .total_kills(guild.total_kills().value())
            .level(guild.level().value())
            .experience(guild.experience().value())
            .guild_color(guild.guild_color().value())
            .staff_g(guild.staff_g().get_id())
            .maybe_color(Some(guild.color().value()).flatten())
            .build()
    }
}

impl From<Faction> for FactionResponse {
    fn from(faction: Faction) -> Self {
        Self::builder()
            .id(faction.id().get_id())
            .name(faction.name().value())
            .build()
    }
}

impl From<UserCurrency> for UserCurrencyResponse {
    fn from(currency: UserCurrency) -> Self {
        Self::builder()
            .id(currency.id().get_id())
            .user_id(currency.user_id().get_id())
            .gold(currency.gold().value())
            .coins(currency.coins().value())
            .diamonds(currency.diamonds().value())
            .crystal(currency.crystal().value())
            .build()
    }
}

impl From<UserItem> for UserItemResponse {
    fn from(item: UserItem) -> Self {
        Self::builder()
            .id(item.id().get_id())
            .user_id(item.user_id().get_id())
            .item_id(item.item_id().get_id())
            .enh_id(item.enh_id().get_id())
            .equipped(item.equipped().value())
            .quantity(item.quantity().value())
            .bank(item.bank().value())
            .maybe_bind(Some(item.bind().value()).flatten())
            .date_purchased(item.date_purchased().value())
            .build()
    }
}

impl From<UserBoost> for UserBoostResponse {
    fn from(boost: UserBoost) -> Self {
        Self::builder()
            .id(boost.id().get_id())
            .user_id(boost.user_id().get_id())
            .cp_boost_expire(boost.cp_boost_expire().value())
            .rep_boost_expire(boost.rep_boost_expire().value())
            .gold_boost_expire(boost.gold_boost_expire().value())
            .exp_boost_expire(boost.exp_boost_expire().value())
            .upgrade_expire(boost.upgrade_expire().value())
            .upgrade_days(boost.upgrade_days().value())
            .upgraded(boost.upgraded().value())
            .build()
    }
}
