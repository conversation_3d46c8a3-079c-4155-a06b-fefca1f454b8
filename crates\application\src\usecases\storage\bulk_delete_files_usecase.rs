use std::sync::Arc;

use reforged_domain::traits::storage_provider::StorageProvider;

use crate::error::ApplicationError;

pub struct BulkDeleteFilesUsecase {
    storage_provider: Arc<dyn StorageProvider>,
}

impl BulkDeleteFilesUsecase {
    pub fn new(storage_provider: Arc<dyn StorageProvider>) -> Self {
        Self { storage_provider }
    }

    pub async fn execute(&self, files: &[&str]) -> Result<(), ApplicationError> {
        let tasks = files
            .into_iter()
            .map(|key| self.storage_provider.delete(key));

        let results = futures_util::future::join_all(tasks).await;

        for result in results {
            result?;
        }

        Ok(())
    }
}
