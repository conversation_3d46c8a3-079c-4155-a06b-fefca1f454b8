use reforged_domain::{
    error::RepositoryError, models::user_livedrop::entity::UserLivedrop as DomainUserLivedrop,
    models::user_livedrop::value_object::UserLivedropId,
    repository::user_livedrop_repository::UserLivedropRepository,
};
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{ActiveModelTrait, ModelTrait};
use sea_orm::{DatabaseConnection, EntityTrait, prelude::async_trait::async_trait};

use crate::models::user_livedrops::{ActiveModel, Entity};

pub struct PostgresUserLivedropRepository {
    db: DatabaseConnection,
}

impl PostgresUserLivedropRepository {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

#[async_trait]
impl UserLivedropRepository for PostgresUserLivedropRepository {
    async fn save(&self, user_livedrop: &DomainUserLivedrop) -> Result<(), RepositoryError> {
        let active_model = ActiveModel {
            id: Set(user_livedrop.id().get_id()),
            user_id: Set(user_livedrop.user_id().as_ref().map(|id| id.get_id())),
            item_id: Set(user_livedrop.item_id().as_ref().map(|id| id.get_id())),
            quantity: Set(user_livedrop.quantity().value()),
            sent: Set(user_livedrop.sent().value()),
            date: Set(user_livedrop.date().value().naive_utc()),
            message: Set(user_livedrop.message().value().clone()),
            achievement_id: Set(user_livedrop
                .achievement_id()
                .as_ref()
                .map(|id| id.get_id())),
            title_id: Set(user_livedrop.title_id().as_ref().map(|id| id.get_id())),
            experience: Set(user_livedrop.experience().value()),
            gold: Set(user_livedrop.gold().value()),
            coins: Set(user_livedrop.coins().value()),
            crystal: Set(user_livedrop.crystal().value()),
            upgrade_days: Set(user_livedrop.upgrade_days().value()),
            bag_slots: Set(user_livedrop.bag_slots().value()),
            bank_slots: Set(user_livedrop.bank_slots().value()),
            house_slots: Set(user_livedrop.house_slots().value()),
            ..Default::default()
        };

        active_model
            .insert(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn update(&self, user_livedrop: &DomainUserLivedrop) -> Result<(), RepositoryError> {
        let model = Entity::find_by_id(user_livedrop.id().get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?
            .ok_or_else(|| RepositoryError::NotFound("UserLivedrop not found".to_string()))?;

        let mut active_model: ActiveModel = model.into();
        active_model.user_id = Set(user_livedrop.user_id().as_ref().map(|id| id.get_id()));
        active_model.item_id = Set(user_livedrop.item_id().as_ref().map(|id| id.get_id()));
        active_model.quantity = Set(user_livedrop.quantity().value());
        active_model.sent = Set(user_livedrop.sent().value());
        active_model.date = Set(user_livedrop.date().value().naive_utc());
        active_model.message = Set(user_livedrop.message().value().clone());
        active_model.achievement_id = Set(user_livedrop
            .achievement_id()
            .as_ref()
            .map(|id| id.get_id()));
        active_model.title_id = Set(user_livedrop.title_id().as_ref().map(|id| id.get_id()));
        active_model.experience = Set(user_livedrop.experience().value());
        active_model.gold = Set(user_livedrop.gold().value());
        active_model.coins = Set(user_livedrop.coins().value());
        active_model.crystal = Set(user_livedrop.crystal().value());
        active_model.upgrade_days = Set(user_livedrop.upgrade_days().value());
        active_model.bag_slots = Set(user_livedrop.bag_slots().value());
        active_model.bank_slots = Set(user_livedrop.bank_slots().value());
        active_model.house_slots = Set(user_livedrop.house_slots().value());

        active_model
            .update(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn delete(&self, id: &UserLivedropId) -> Result<(), RepositoryError> {
        let model = Entity::find_by_id(id.get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?
            .ok_or_else(|| RepositoryError::NotFound("UserLivedrop not found".to_string()))?;

        model
            .delete(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }
}
