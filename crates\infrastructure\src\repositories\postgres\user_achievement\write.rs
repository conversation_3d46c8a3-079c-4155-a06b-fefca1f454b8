use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_achievement::entity::UserAchievement;
use reforged_domain::models::user_achievement::value_object::UserAchievementId;
use reforged_domain::repository::user_achievement_repository::UserAchievementRepository;
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, DatabaseConnection, EntityTrait, IntoActiveModel, ModelTrait,
    QueryFilter, prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::models::user_achievements::{
    ActiveModel as UserAchievementsActiveModel, Column as UserAchievementsColumn,
    Entity as UserAchievements,
};

#[allow(dead_code)]
pub struct PostgresUserAchievementRepository {
    pool: DatabaseConnection,
}

impl PostgresUserAchievementRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserAchievementRepository for PostgresUserAchievementRepository {
    async fn save(&self, entity: &UserAchievement) -> Result<(), RepositoryError> {
        let model = UserAchievementsActiveModel {
            id: Set(entity.id().get_id()),
            user_id: Set(entity.user_id().get_id()),
            achievement_id: Set(entity.achievement_id().get_id()),
            date: Set(entity.date().value().naive_utc()),
        };

        model.insert(&self.pool).await.map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn update(&self, entity: &UserAchievement) -> Result<(), RepositoryError> {
        let existing_model = UserAchievements::find()
            .filter(UserAchievementsColumn::Id.eq(entity.id().get_id()))
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?
            .ok_or(RepositoryError::NotFound(format!(
                "UserAchievement with id {}",
                entity.id().get_id()
            )))?;

        let mut active_model = existing_model.into_active_model();

        active_model.user_id = Set(entity.user_id().get_id());
        active_model.achievement_id = Set(entity.achievement_id().get_id());
        active_model.date = Set(entity.date().value().naive_utc());

        active_model
            .update(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn delete(&self, id: &UserAchievementId) -> Result<(), RepositoryError> {
        let existing_model = UserAchievements::find()
            .filter(UserAchievementsColumn::Id.eq(id.get_id()))
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?
            .ok_or(RepositoryError::NotFound(format!(
                "UserAchievement with id {}",
                id.get_id()
            )))?;

        existing_model
            .delete(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }
}
