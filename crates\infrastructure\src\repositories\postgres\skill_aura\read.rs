use async_trait::async_trait;
use reforged_domain::models::aura::value_object::AuraId;
use reforged_domain::models::skill::value_object::SkillId;
use reforged_domain::models::skill_aura::value_object::SkillAuraId;
use reforged_domain::{
    error::RepositoryError, models::skill_aura::entity::SkillAura,
    repository::skill_aura_repository::SkillAuraReadRepository,
};
use reforged_shared::IdTrait;
use sea_orm::ColumnTrait;
use sea_orm::DatabaseConnection;
use sea_orm::EntityTrait;
use sea_orm::QueryFilter;

use crate::SeaORMErr;
use crate::mappers::skill_aura_mapper::SkillAuraDbModelMapper;

use crate::models::skill_auras::Column as SkillAurasModelColumn;
use crate::models::skill_auras::Entity as SkillAuras;

#[allow(dead_code)]
pub struct PostgresSkillAuraReadRepository {
    pool: DatabaseConnection,
}

impl PostgresSkillAuraReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl SkillAuraReadRepository for PostgresSkillAuraReadRepository {
    async fn find_by_id(&self, id: &SkillAuraId) -> Result<Option<SkillAura>, RepositoryError> {
        let skill_aura = SkillAuras::find()
            .filter(SkillAurasModelColumn::Id.eq(id.get_id()))
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        match skill_aura {
            Some(skill_aura_model) => {
                let mapped_skill_aura = SkillAuraDbModelMapper::new(skill_aura_model);
                let skill_aura = SkillAura::from(mapped_skill_aura);
                Ok(Some(skill_aura))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<SkillAura>, RepositoryError> {
        let skill_auras = SkillAuras::find()
            .all(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        let mapped_skill_auras = skill_auras
            .into_iter()
            .map(SkillAuraDbModelMapper::new)
            .collect::<Vec<_>>();

        let skill_auras = mapped_skill_auras
            .into_iter()
            .map(SkillAura::from)
            .collect::<Vec<_>>();

        Ok(skill_auras)
    }

    async fn find_by_skill_id(
        &self,
        skill_id: &SkillId,
    ) -> Result<Vec<SkillAura>, RepositoryError> {
        let skill_auras = SkillAuras::find()
            .filter(SkillAurasModelColumn::SkillId.eq(skill_id.get_id()))
            .all(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        let mapped_skill_auras = skill_auras
            .into_iter()
            .map(SkillAuraDbModelMapper::new)
            .collect::<Vec<_>>();

        let skill_auras = mapped_skill_auras
            .into_iter()
            .map(SkillAura::from)
            .collect::<Vec<_>>();

        Ok(skill_auras)
    }

    async fn find_by_aura_id(&self, aura_id: &AuraId) -> Result<Vec<SkillAura>, RepositoryError> {
        let skill_auras = SkillAuras::find()
            .filter(SkillAurasModelColumn::AuraId.eq(aura_id.get_id()))
            .all(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        let mapped_skill_auras = skill_auras
            .into_iter()
            .map(SkillAuraDbModelMapper::new)
            .collect::<Vec<_>>();

        let skill_auras = mapped_skill_auras
            .into_iter()
            .map(SkillAura::from)
            .collect::<Vec<_>>();

        Ok(skill_auras)
    }
}
