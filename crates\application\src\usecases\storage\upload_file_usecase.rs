use std::sync::Arc;

use reforged_domain::{
    events::file_events::{FileUploadFailed, FileUploadProgress, FileUploadStarted, FileUploaded},
    traits::storage_provider::StorageProvider,
};
use reforged_shared::uuid_generator::uuid_now;

use crate::{
    error::ApplicationError,
    traits::{BrokerServicePublisher, PasetoClaimPurpose, TokenService},
};

pub struct UploadFileUsecase {
    storage_provider: Arc<dyn StorageProvider>,
    token_service: Arc<dyn TokenService>,
    broker_service: Arc<dyn BrokerServicePublisher>,
}

impl UploadFileUsecase {
    pub fn new(
        storage_provider: Arc<dyn StorageProvider>,
        token_service: Arc<dyn TokenService>,
        broker_service: Arc<dyn BrokerServicePublisher>,
    ) -> Self {
        Self {
            storage_provider,
            token_service,
            broker_service,
        }
    }
}

impl UploadFileUsecase {
    pub async fn execute(
        &self,
        token: &str,
        directory: &str,
        key: &str,
        file: &[u8],
    ) -> Result<(), ApplicationError> {
        let token_service = self.token_service.clone();
        let claims =
            token_service.validate_token(token.to_string(), PasetoClaimPurpose::FileUpload)?;
        let user_id = claims.id;

        let processor =
            FileUploadProcessor::new(self.storage_provider.clone(), self.broker_service.clone());

        processor.process(user_id, directory, key, file).await?;

        Ok(())
    }
}

#[derive(Clone)]
pub struct FileUploadProcessor {
    storage_provider: Arc<dyn StorageProvider>,
    broker_service: Arc<dyn BrokerServicePublisher>,
}

impl FileUploadProcessor {
    pub fn new(
        storage_provider: Arc<dyn StorageProvider>,
        broker_service: Arc<dyn BrokerServicePublisher>,
    ) -> Self {
        Self {
            storage_provider,
            broker_service,
        }
    }
}

impl FileUploadProcessor {
    pub async fn process(
        &self,
        user_id: uuid::Uuid,
        directory: &str,
        key: &str,
        file: &[u8],
    ) -> Result<(), ApplicationError> {
        let path = if !directory.is_empty() {
            format!("{}/{}", directory, key)
        } else {
            key.to_string()
        };

        let has_file = self.storage_provider.has_file(&path).await;
        let path = if has_file.is_ok() {
            format!("{}/{}-{}", directory, uuid_now(), key)
        } else {
            path
        };

        self.storage_provider.create(&path).await?;
        let mut file_handle = self.storage_provider.get_file_handle(&path).await?;

        let file_size = file.len();

        let id = uuid_now();

        let start = FileUploadStarted {
            id,
            filename: key.to_string(),
            file_size: file_size,
            uploaded_by: user_id,
            started_at: chrono::Utc::now(),
        };
        let start_json =
            serde_json::to_value(start).map_err(|_| ApplicationError::JsonSerializationError)?;

        _ = self
            .broker_service
            .publish("FileUploadStarted", start_json)
            .await;
        let mut total_bytes_written = 0;

        for chunk in file.chunks(2097152) {
            let upload_result = self.storage_provider.save(&mut file_handle, chunk).await;

            match upload_result {
                Ok(bytes_written) => {
                    total_bytes_written += bytes_written;

                    let progress = FileUploadProgress {
                        id,
                        filename: key.to_string(),
                        bytes_uploaded: total_bytes_written,
                        file_size: file_size,
                        uploaded_by: user_id,
                    };
                    let progress_json = serde_json::to_value(progress)
                        .map_err(|_| ApplicationError::JsonSerializationError)?;

                    _ = self
                        .broker_service
                        .publish("FileUploadProgress", progress_json)
                        .await;
                }
                Err(e) => {
                    let err = e.to_string();
                    let error = FileUploadFailed {
                        id,
                        filename: key.to_string(),
                        error_message: err.clone(),
                        uploaded_by: user_id,
                    };
                    let error_json = serde_json::to_value(error)
                        .map_err(|_| ApplicationError::JsonSerializationError)?;

                    _ = self
                        .broker_service
                        .publish("FileUploadFailed", error_json)
                        .await;

                    return Err(ApplicationError::WriteError(err));
                }
            }
        }

        let uploaded = FileUploaded {
            id,
            filename: key.to_string(),
            file_size: file_size,
            uploaded_by: user_id,
            uploaded_at: chrono::Utc::now(),
        };
        let uploaded_json =
            serde_json::to_value(uploaded).map_err(|_| ApplicationError::JsonSerializationError)?;

        _ = self
            .broker_service
            .publish("FileUploaded", uploaded_json)
            .await;

        Ok(())
    }
}
