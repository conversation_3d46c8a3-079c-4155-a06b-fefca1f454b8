use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use crate::models::directory::value_object::*;
use crate::models::file::entity::File;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct Directory {
    id: DirectoryId,
    name: DirectoryName,
    path: DirectoryPath,
    files: Vec<File>,
    directories: Vec<Directory>,
}
