use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_guild::entity::UserGuild;
use reforged_domain::models::user_guild::value_object::UserGuildId;
use reforged_domain::repository::user_guild_repository::UserGuildReadRepository;
use reforged_shared::IdTrait;
use sea_orm::{DatabaseConnection, EntityTrait, prelude::async_trait::async_trait};

use crate::SeaORMErr;
use crate::mappers::user_guild_mapper::UserGuildDbModelMapper;
use crate::models::user_guilds::Entity as UserGuilds;

#[allow(dead_code)]
pub struct PostgresUserGuildReadRepository {
    pool: DatabaseConnection,
}

impl PostgresUserGuildReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserGuildReadRepository for PostgresUserGuildReadRepository {
    async fn find_by_id(&self, id: &UserGuildId) -> Result<Option<UserGuild>, RepositoryError> {
        let user_guild = UserGuilds::find_by_id(id.get_id())
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_guild {
            Some(model) => {
                let mapped = UserGuild::from(UserGuildDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<UserGuild>, RepositoryError> {
        let user_guilds = UserGuilds::find()
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserGuild> = user_guilds
            .into_iter()
            .map(|model| UserGuild::from(UserGuildDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }
}
