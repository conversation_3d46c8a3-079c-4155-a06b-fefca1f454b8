use reforged_domain::{
    error::RepositoryError, models::title::entity::Title as DomainTitle,
    models::title::value_object::TitleId, repository::title_repository::TitleRepository,
};
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{ActiveModelTrait, ModelTrait};
use sea_orm::{DatabaseConnection, EntityTrait, prelude::async_trait::async_trait};

use crate::models::titles::{ActiveModel, Entity};

pub struct PostgresTitleRepository {
    db: DatabaseConnection,
}

impl PostgresTitleRepository {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

#[async_trait]
impl TitleRepository for PostgresTitleRepository {
    async fn save(&self, title: &DomainTitle) -> Result<(), RepositoryError> {
        let active_model = ActiveModel {
            id: Set(title.id().get_id()),
            name: Set(title.name().value().clone()),
            description: Set(title.description().value().clone()),
            color: Set(title.color().value().clone()),
            strength: Set(title.strength().value()),
            intellect: Set(title.intellect().value()),
            endurance: Set(title.endurance().value()),
            dexterity: Set(title.dexterity().value()),
            wisdom: Set(title.wisdom().value()),
            luck: Set(title.luck().value()),
            role_id: Set(title.role_id().get_id()),
            ..Default::default()
        };

        active_model
            .insert(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn update(&self, title: &DomainTitle) -> Result<(), RepositoryError> {
        let model = Entity::find_by_id(title.id().get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?
            .ok_or_else(|| RepositoryError::NotFound("Title not found".to_string()))?;

        let mut active_model: ActiveModel = model.into();
        active_model.name = Set(title.name().value().clone());
        active_model.description = Set(title.description().value().clone());
        active_model.color = Set(title.color().value().clone());
        active_model.strength = Set(title.strength().value());
        active_model.intellect = Set(title.intellect().value());
        active_model.endurance = Set(title.endurance().value());
        active_model.dexterity = Set(title.dexterity().value());
        active_model.wisdom = Set(title.wisdom().value());
        active_model.luck = Set(title.luck().value());
        active_model.role_id = Set(title.role_id().get_id());

        active_model
            .update(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn delete(&self, id: &TitleId) -> Result<(), RepositoryError> {
        let model = Entity::find_by_id(id.get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?
            .ok_or_else(|| RepositoryError::NotFound("Title not found".to_string()))?;

        model
            .delete(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }
}
