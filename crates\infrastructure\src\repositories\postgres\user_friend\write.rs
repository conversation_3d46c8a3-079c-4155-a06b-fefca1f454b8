use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_friend::entity::UserFriend;
use reforged_domain::models::user_friend::value_object::UserFriendId;
use reforged_domain::repository::user_friend_repository::UserFriendRepository;
use reforged_shared::IdTrait;
use sea_orm::ActiveValue::Set;
use sea_orm::{
    ActiveModelTrait, DatabaseConnection, EntityTrait, IntoActiveModel,
    prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::models::user_friends::{ActiveModel as UserFriendsActiveModel, Entity as UserFriends};

#[allow(dead_code)]
pub struct PostgresUserFriendRepository {
    pool: DatabaseConnection,
}

impl PostgresUserFriendRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserFriendRepository for PostgresUserFriendRepository {
    async fn save(&self, entity: &UserFriend) -> Result<(), RepositoryError> {
        let model = UserFriendsActiveModel {
            id: Set(entity.id().get_id()),
            user_id: Set(entity.user_id().get_id()),
            friend_id: Set(entity.friend_id().get_id()),
        };

        model.insert(&self.pool).await.map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn update(&self, entity: &UserFriend) -> Result<(), RepositoryError> {
        let existing_model = UserFriends::find_by_id(entity.id().get_id())
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?
            .ok_or(RepositoryError::NotFound(format!(
                "UserFriend with id {}",
                entity.id().get_id()
            )))?;

        let mut active_model = existing_model.into_active_model();

        active_model.user_id = Set(entity.user_id().get_id());
        active_model.friend_id = Set(entity.friend_id().get_id());

        active_model
            .update(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn delete(&self, id: &UserFriendId) -> Result<(), RepositoryError> {
        let result = UserFriends::delete_by_id(id.get_id())
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if result.rows_affected == 0 {
            return Err(RepositoryError::NotFound(format!(
                "UserFriend with id {}",
                id.get_id()
            )));
        }

        Ok(())
    }
}
