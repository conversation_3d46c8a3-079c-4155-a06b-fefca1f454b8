use async_trait::async_trait;
use reforged_domain::error::RepositoryError;
use reforged_domain::models::war::entity::War;
use reforged_domain::models::war::value_object::WarId;
use reforged_domain::repository::war_repository::WarRepository;
use reforged_shared::{IdTrait, Value};
use sea_orm::EntityTrait;
use sea_orm::{ActiveModelTrait, DatabaseConnection, Set};

use crate::SeaORMErr;
use crate::models::wars::{ActiveModel as WarActiveModel, Entity as Wars};

pub struct PostgresWarRepository {
    pool: DatabaseConnection,
}

impl PostgresWarRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl WarRepository for PostgresWarRepository {
    async fn save(&self, war: &War) -> Result<(), RepositoryError> {
        let am = WarActiveModel {
            id: Set(war.id().get_id()),
            name: Set(war.name().value()),
            points: Set(war.points().value()),
            max_points: Set(war.max_points().value()),
        };

        let _ = am.insert(&self.pool).await.map_err(SeaORMErr::from)?;
        Ok(())
    }

    async fn update(&self, war: &War) -> Result<(), RepositoryError> {
        let am = WarActiveModel {
            id: Set(war.id().get_id()),
            name: Set(war.name().value()),
            points: Set(war.points().value()),
            max_points: Set(war.max_points().value()),
        };

        let _ = am.update(&self.pool).await.map_err(SeaORMErr::from)?;
        Ok(())
    }

    async fn delete(&self, id: &WarId) -> Result<(), RepositoryError> {
        let am = WarActiveModel {
            id: Set(id.get_id()),
            name: Set(String::new()),
            points: Set(0),
            max_points: Set(0),
        };

        let result = Wars::delete(am)
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if result.rows_affected == 0 {
            return Err(RepositoryError::NotFound(format!("with id {}", id)));
        }

        Ok(())
    }
}
