[package]
name = "reforged-ws"
version = "0.1.0"
edition = "2024"

[dependencies]
actix.workspace = true
actix-web.workspace = true
actix-ws.workspace = true
actix-broker.workspace = true
tokio.workspace = true
tracing.workspace = true
tracing-subscriber.workspace = true
futures-util.workspace = true
uuid.workspace = true
serde.workspace = true
serde_json.workspace = true
chrono.workspace = true
async-trait.workspace = true
lapin.workspace = true

reforged-domain = { path = "../domain" }
reforged-shared = { path = "../shared" }
reforged-application = { path = "../application" }
