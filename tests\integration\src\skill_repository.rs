#[cfg(test)]
use reforged_domain::repository::skill_repository::{MockSkillReadRepository, MockSkillRepository};
#[cfg(test)]
use reforged_domain::{
    error::RepositoryError,
    models::skill::{entity::Skill, value_object::*},
};
#[cfg(test)]
use reforged_shared::uuid_generator::{uuid_from_secs, uuid_now};
#[cfg(test)]
use reforged_shared::{IdTrait, Value};

// Sample service function that uses the repository
#[cfg(test)]
async fn get_skill_by_id(
    repo: &impl reforged_domain::repository::skill_repository::SkillReadRepository,
    id: &SkillId,
) -> Result<Option<Skill>, RepositoryError> {
    repo.find_by_id(id).await
}

// Sample service function that uses both repositories
#[cfg(test)]
async fn update_skill_damage(
    read_repo: &impl reforged_domain::repository::skill_repository::SkillReadRepository,
    write_repo: &impl reforged_domain::repository::skill_repository::SkillRepository,
    id: &SkillId,
    new_damage: f64,
) -> Result<(), RepositoryError> {
    let mut skill = match read_repo.find_by_id(id).await? {
        Some(skill) => skill,
        None => {
            return Err(RepositoryError::NotFound(format!(
                "Skill with id {} not found",
                id
            )));
        }
    };

    skill.set_damage(Damage::new(new_damage));
    write_repo.update(&skill).await
}

#[tokio::test]
async fn test_find_by_id() {
    // Create a mock for the repository
    let mut mock_repo = MockSkillReadRepository::new();

    // Create test data
    let id = uuid_now();
    let skill_id = SkillId::new(id);

    let test_skill = Skill::builder()
        .id(skill_id.clone())
        .name(SkillName::new("Test Skill"))
        .damage(Damage::new(100.0))
        .animation(Animation::new("test_animation"))
        .description(SkillDescription::new("Test Description"))
        .mana(Mana::new(50))
        .mana_back(ManaBack::new(10))
        .life_steal(LifeSteal::new(5.0))
        .icon(Icon::new("test_icon.png"))
        .range(Range::new(10))
        .dsrc(Dsrc::new("test_dsrc"))
        .reference(Reference::new("test_reference"))
        .target(Target::new("test_target"))
        .effects(Effects::new("test_effects"))
        .skill_type(SkillType::new("test_type"))
        .strl(Strl::new("test_strl"))
        .cooldown(Cooldown::new(30))
        .hit_targets(HitTargets::new(3))
        .pet(Pet::new(false))
        .chance(Chance::new(Some(75.0)))
        .show_damage(ShowDamage::new(true))
        .build();

    // Set expectation on the mock
    mock_repo
        .expect_find_by_id()
        .with(mockall::predicate::eq(skill_id.clone()))
        .times(1)
        .returning(move |_| Ok(Some(test_skill.clone())));

    // Call the service function with our mock
    let result = get_skill_by_id(&mock_repo, &skill_id).await;

    // Assert the result
    assert!(result.is_ok());
    let skill = result.unwrap().unwrap();
    assert_eq!(skill.id().get_id(), skill_id.get_id());
    assert_eq!(skill.name().value(), "Test Skill");
    assert_eq!(skill.damage().value(), 100.0);
}

#[tokio::test]
async fn test_find_by_id_not_found() {
    // Create a mock for the repository
    let mut mock_repo = MockSkillReadRepository::new();
    let id = uuid_from_secs(999);
    let skill_id = SkillId::new(id);

    // Set expectation for a not found result
    mock_repo
        .expect_find_by_id()
        .with(mockall::predicate::eq(skill_id.clone()))
        .times(1)
        .returning(|_| Ok(None));

    // Call the service function with our mock
    let result = get_skill_by_id(&mock_repo, &skill_id).await;

    // Assert the result
    assert!(result.is_ok());
    assert!(result.unwrap().is_none());
}

#[tokio::test]
async fn test_update_skill_damage() {
    // Create mocks for both repositories
    let mut mock_read_repo = MockSkillReadRepository::new();
    let mut mock_write_repo = MockSkillRepository::new();

    // Create test data
    let id = uuid_from_secs(1);
    let skill_id = SkillId::new(id);
    let old_damage = 100.0;
    let new_damage = 150.0;

    let test_skill = Skill::builder()
        .id(skill_id.clone())
        .name(SkillName::new("Test Skill"))
        .damage(Damage::new(old_damage))
        .animation(Animation::new("test_animation"))
        .description(SkillDescription::new("Test Description"))
        .mana(Mana::new(50))
        .mana_back(ManaBack::new(10))
        .life_steal(LifeSteal::new(5.0))
        .icon(Icon::new("test_icon.png"))
        .range(Range::new(10))
        .dsrc(Dsrc::new("test_dsrc"))
        .reference(Reference::new("test_reference"))
        .target(Target::new("test_target"))
        .effects(Effects::new("test_effects"))
        .skill_type(SkillType::new("test_type"))
        .strl(Strl::new("test_strl"))
        .cooldown(Cooldown::new(30))
        .hit_targets(HitTargets::new(3))
        .pet(Pet::new(false))
        .chance(Chance::new(Some(75.0)))
        .show_damage(ShowDamage::new(true))
        .build();

    // Set expectations
    mock_read_repo
        .expect_find_by_id()
        .with(mockall::predicate::eq(skill_id.clone()))
        .times(1)
        .returning(move |_| Ok(Some(test_skill.clone())));

    mock_write_repo
        .expect_update()
        .withf(move |skill| skill.damage().value() == new_damage)
        .times(1)
        .returning(|_| Ok(()));

    // Call the service function with our mocks
    let result =
        update_skill_damage(&mock_read_repo, &mock_write_repo, &skill_id, new_damage).await;

    // Assert the result
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_update_skill_damage_not_found() {
    // Create mocks for both repositories
    let mut mock_read_repo = MockSkillReadRepository::new();
    let mock_write_repo = MockSkillRepository::new();

    // Set expectations for a not found result
    let id = uuid_from_secs(999);
    let skill_id = SkillId::new(id);

    mock_read_repo
        .expect_find_by_id()
        .with(mockall::predicate::eq(skill_id.clone()))
        .times(1)
        .returning(|_| Ok(None));

    // Call the service function with our mocks
    let result = update_skill_damage(&mock_read_repo, &mock_write_repo, &skill_id, 200.0).await;

    // Assert the result
    assert!(result.is_err());
    assert!(matches!(result.unwrap_err(), RepositoryError::NotFound(_)));
}
