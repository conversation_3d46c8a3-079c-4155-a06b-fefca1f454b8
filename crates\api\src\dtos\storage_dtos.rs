use actix_multipart::form::{MultipartForm, json::<PERSON><PERSON> as MP<PERSON><PERSON>, tempfile::TempFile};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct CreateDirectoryDTO {
    #[validate(length(min = 1, max = 255))]
    pub path: String,
}

#[derive(Debug, Deserialize)]
pub struct GetUploadSessionDTO {
    pub token: String,
}

#[derive(Debug, Deserialize, Validate)]
pub struct MoveFileDTO {
    #[validate(length(min = 1, max = 255))]
    pub new_path: String,
}

#[derive(Debug, Deserialize, Validate)]
pub struct BulkMoveFileDTO {
    pub files: Vec<BulkMoveFilePayload>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct BulkMoveFilePayload {
    #[validate(length(min = 1, max = 255))]
    pub old_path: String,
    #[validate(length(min = 1, max = 255))]
    pub new_path: String,
}

#[derive(Debug, Deserialize, Validate)]
pub struct MoveDirectoryDTO {
    #[validate(length(min = 1, max = 255))]
    pub new_path: String,
}

#[derive(Debug, Deserialize, Validate)]
pub struct BulkMoveDirectoryDTO {
    pub directories: Vec<BulkMoveDirectoryPayload>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct BulkMoveDirectoryPayload {
    #[validate(length(min = 1, max = 255))]
    pub old_path: String,
    #[validate(length(min = 1, max = 255))]
    pub new_path: String,
}

#[derive(Debug, Deserialize, Validate)]
pub struct BulkDeleteDirectoryDTO {
    #[validate(length(min = 1, max = 255))]
    pub directories: Vec<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct BulkDeleteFileDTO {
    #[validate(length(min = 1, max = 255))]
    pub files: Vec<String>,
}

#[derive(Debug, Deserialize)]
pub struct Metadata {
    pub directory: Option<String>,
    pub token: String,
}

#[derive(Debug, MultipartForm)]
pub struct UploadForm {
    #[multipart(limit = "100 MiB")]
    pub file: TempFile,
    pub json: MPJson<Metadata>,
}

#[derive(Debug, MultipartForm)]
pub struct BulkUploadForm {
    #[multipart(limit = "100 MiB")]
    #[multipart(rename = "file")]
    pub files: Vec<TempFile>,
    pub json: MPJson<Metadata>,
}
