use std::sync::Arc;

use reforged_domain::traits::storage_provider::StorageProvider;

use crate::error::ApplicationError;

pub struct BulkMoveDirectories {
    storage_provider: Arc<dyn StorageProvider>,
}

impl BulkMoveDirectories {
    pub fn new(storage_provider: Arc<dyn StorageProvider>) -> Self {
        Self { storage_provider }
    }

    pub async fn execute(&self, directories: &[(&str, &str)]) -> Result<(), ApplicationError> {
        let tasks = directories
            .into_iter()
            .map(|(old_path, new_path)| self.storage_provider.rename_directory(old_path, new_path));

        let results = futures_util::future::join_all(tasks).await;

        for result in results {
            result?;
        }

        Ok(())
    }
}
