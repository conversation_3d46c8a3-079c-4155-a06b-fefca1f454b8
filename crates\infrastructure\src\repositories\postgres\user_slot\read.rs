use reforged_domain::error::RepositoryError;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::user_slot::entity::UserSlot;
use reforged_domain::models::user_slot::value_object::UserSlotId;
use reforged_domain::repository::user_slot_repository::UserSlotReadRepository;
use reforged_shared::IdTrait;
use sea_orm::{
    ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::mappers::user_slot_mapper::UserSlotDbModelMapper;
use crate::models::user_slots::{Column as UserSlotsColumn, Entity as UserSlots};

#[allow(dead_code)]
pub struct PostgresUserSlotReadRepository {
    pool: DatabaseConnection,
}

impl PostgresUserSlotReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserSlotReadRepository for PostgresUserSlotReadRepository {
    async fn find_by_id(&self, id: &UserSlotId) -> Result<Option<UserSlot>, RepositoryError> {
        let user_slot = UserSlots::find_by_id(id.get_id())
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_slot {
            Some(model) => {
                let mapped = UserSlot::from(UserSlotDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<UserSlot>, RepositoryError> {
        let user_slots = UserSlots::find()
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mapped: Vec<UserSlot> = user_slots
            .into_iter()
            .map(|model| UserSlot::from(UserSlotDbModelMapper::new(model)))
            .collect();

        Ok(mapped)
    }

    async fn find_by_user_id(&self, user_id: &UserId) -> Result<Option<UserSlot>, RepositoryError> {
        let user_slot = UserSlots::find()
            .filter(UserSlotsColumn::UserId.eq(user_id.get_id()))
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        match user_slot {
            Some(model) => {
                let mapped = UserSlot::from(UserSlotDbModelMapper::new(model));
                Ok(Some(mapped))
            }
            None => Ok(None),
        }
    }
}
