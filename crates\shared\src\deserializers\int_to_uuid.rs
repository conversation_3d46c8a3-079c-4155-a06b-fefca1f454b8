use std::str::FromStr;

use super::visitors::{OptionalStringOrNumberVisitor, StringOrNumberVisitor};

pub fn int_to_uuid<'de, D>(deserializer: D) -> Result<uuid::Uuid, D::Error>
where
    D: serde::Deserializer<'de>,
{
    let data = deserializer.deserialize_any(StringOrNumberVisitor)?;
    let uuid = uuid::Uuid::from_str(&data).map_err(|e| serde::de::Error::custom(e))?;

    Ok(uuid)
}

pub fn optional_int_to_uuid<'de, D>(deserializer: D) -> Result<Option<uuid::Uuid>, D::Error>
where
    D: serde::Deserializer<'de>,
{
    deserializer
        .deserialize_option(OptionalStringOrNumberVisitor)?
        .map(|value| uuid::Uuid::from_str(&value).map_err(serde::de::Error::custom))
        .transpose()
}
