use reforged_domain::{
    error::RepositoryError, models::user::value_object::UserId,
    models::user_report::entity::UserReport as DomainUserReport,
    models::user_report::value_object::UserReportId,
    repository::user_report_repository::UserReportReadRepository,
};
use reforged_shared::IdTrait;
use sea_orm::{
    ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, prelude::async_trait::async_trait,
};

use crate::{
    mappers::user_report_mapper::UserReportDbModelMapper,
    models::user_reports::{Column, Entity},
};

pub struct PostgresUserReportReadRepository {
    db: DatabaseConnection,
}

impl PostgresUserReportReadRepository {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

#[async_trait]
impl UserReportReadRepository for PostgresUserReportReadRepository {
    async fn find_by_id(
        &self,
        id: &UserReportId,
    ) -> Result<Option<DomainUserReport>, RepositoryError> {
        let model = Entity::find_by_id(id.get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        match model {
            Some(model) => {
                let mapper = UserReportDbModelMapper::new(model);
                Ok(Some(mapper.into()))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<DomainUserReport>, RepositoryError> {
        let models = Entity::find()
            .all(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        let user_reports = models
            .into_iter()
            .map(|model| {
                let mapper = UserReportDbModelMapper::new(model);
                mapper.into()
            })
            .collect();

        Ok(user_reports)
    }

    async fn find_by_user_id(
        &self,
        user_id: &UserId,
    ) -> Result<Vec<DomainUserReport>, RepositoryError> {
        let models = Entity::find()
            .filter(Column::UserId.eq(user_id.get_id()))
            .all(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        let user_reports = models
            .into_iter()
            .map(|model| {
                let mapper = UserReportDbModelMapper::new(model);
                mapper.into()
            })
            .collect();

        Ok(user_reports)
    }

    async fn find_by_reported_user_id(
        &self,
        reported_user_id: &UserId,
    ) -> Result<Vec<DomainUserReport>, RepositoryError> {
        let models = Entity::find()
            .filter(Column::TargetName.eq(format!("user_{}", reported_user_id.get_id())))
            .all(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        let user_reports = models
            .into_iter()
            .map(|model| {
                let mapper = UserReportDbModelMapper::new(model);
                mapper.into()
            })
            .collect();

        Ok(user_reports)
    }
}
