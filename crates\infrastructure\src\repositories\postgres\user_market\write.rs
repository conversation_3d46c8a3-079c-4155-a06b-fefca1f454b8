use reforged_domain::{
    error::RepositoryError, models::user_market::entity::UserMarket as DomainUserMarket,
    models::user_market::value_object::UserMarketId,
    repository::user_market_repository::UserMarketRepository,
};
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{ActiveModelTrait, ModelTrait};
use sea_orm::{DatabaseConnection, EntityTrait, prelude::async_trait::async_trait};

use crate::models::user_markets::{ActiveModel, Entity};

pub struct PostgresUserMarketRepository {
    db: DatabaseConnection,
}

impl PostgresUserMarketRepository {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

#[async_trait]
impl UserMarketRepository for PostgresUserMarketRepository {
    async fn save(&self, user_market: &DomainUserMarket) -> Result<(), RepositoryError> {
        let market_type = match user_market.market_type() {
            reforged_domain::models::user_market::value_object::MarketType::Vending => {
                crate::models::sea_orm_active_enums::MarketType::Vending
            }
            reforged_domain::models::user_market::value_object::MarketType::Auction => {
                crate::models::sea_orm_active_enums::MarketType::Auction
            }
        };

        let active_model = ActiveModel {
            id: Set(user_market.id().get_id()),
            user_id: Set(user_market.user_id().get_id()),
            item_id: Set(user_market.item_id().get_id()),
            enh_id: Set(user_market.enh_id().get_id()),
            datetime: Set(user_market.datetime().value().naive_utc()),
            buyer_id: Set(user_market.buyer_id().as_ref().map(|id| id.get_id())),
            coins: Set(user_market.coins().value()),
            gold: Set(user_market.gold().value()),
            quantity: Set(user_market.quantity().value()),
            status: Set(user_market.status().value()),
            r#type: Set(market_type),
            ..Default::default()
        };

        active_model
            .insert(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn update(&self, user_market: &DomainUserMarket) -> Result<(), RepositoryError> {
        let model = Entity::find_by_id(user_market.id().get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?
            .ok_or_else(|| RepositoryError::NotFound("UserMarket not found".to_string()))?;

        let market_type = match user_market.market_type() {
            reforged_domain::models::user_market::value_object::MarketType::Vending => {
                crate::models::sea_orm_active_enums::MarketType::Vending
            }
            reforged_domain::models::user_market::value_object::MarketType::Auction => {
                crate::models::sea_orm_active_enums::MarketType::Auction
            }
        };

        let mut active_model: ActiveModel = model.into();
        active_model.user_id = Set(user_market.user_id().get_id());
        active_model.item_id = Set(user_market.item_id().get_id());
        active_model.enh_id = Set(user_market.enh_id().get_id());
        active_model.datetime = Set(user_market.datetime().value().naive_utc());
        active_model.buyer_id = Set(user_market.buyer_id().as_ref().map(|id| id.get_id()));
        active_model.coins = Set(user_market.coins().value());
        active_model.gold = Set(user_market.gold().value());
        active_model.quantity = Set(user_market.quantity().value());
        active_model.status = Set(user_market.status().value());
        active_model.r#type = Set(market_type);

        active_model
            .update(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn delete(&self, id: &UserMarketId) -> Result<(), RepositoryError> {
        let model = Entity::find_by_id(id.get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?
            .ok_or_else(|| RepositoryError::NotFound("UserMarket not found".to_string()))?;

        model
            .delete(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        Ok(())
    }
}
