use reforged_domain::{
    error::RepositoryError, models::user::value_object::UserId,
    models::user_login::entity::UserLogin as DomainUserLogin,
    models::user_login::value_object::UserLoginId,
    repository::user_login_repository::UserLoginReadRepository,
};
use reforged_shared::IdTrait;
use sea_orm::{
    ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, prelude::async_trait::async_trait,
};

use crate::{
    mappers::user_login_mapper::UserLoginDbModelMapper,
    models::user_logins::{Column, Entity},
};

pub struct PostgresUserLoginReadRepository {
    db: DatabaseConnection,
}

impl PostgresUserLoginReadRepository {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

#[async_trait]
impl UserLoginReadRepository for PostgresUserLoginReadRepository {
    async fn find_by_id(
        &self,
        id: &UserLoginId,
    ) -> Result<Option<DomainUserLogin>, RepositoryError> {
        let model = Entity::find_by_id(id.get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        match model {
            Some(model) => {
                let mapper = UserLoginDbModelMapper::new(model);
                Ok(Some(mapper.into()))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<DomainUserLogin>, RepositoryError> {
        let models = Entity::find()
            .all(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        let user_logins = models
            .into_iter()
            .map(|model| {
                let mapper = UserLoginDbModelMapper::new(model);
                mapper.into()
            })
            .collect();

        Ok(user_logins)
    }

    async fn find_by_user_id(
        &self,
        user_id: &UserId,
    ) -> Result<Vec<DomainUserLogin>, RepositoryError> {
        let models = Entity::find()
            .filter(Column::UserId.eq(user_id.get_id()))
            .all(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        let user_logins = models
            .into_iter()
            .map(|model| {
                let mapper = UserLoginDbModelMapper::new(model);
                mapper.into()
            })
            .collect();

        Ok(user_logins)
    }
}
