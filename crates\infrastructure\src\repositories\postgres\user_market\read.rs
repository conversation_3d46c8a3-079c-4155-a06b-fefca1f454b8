use reforged_domain::{
    error::RepositoryError,
    models::user::value_object::UserId,
    models::user_market::entity::UserMarket as DomainUserMarket,
    models::user_market::value_object::{MarketType, UserMarketId},
    repository::user_market_repository::UserMarketReadRepository,
};
use reforged_shared::IdTrait;
use sea_orm::{
    ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, prelude::async_trait::async_trait,
};

use crate::{
    mappers::user_market_mapper::UserMarketDbModelMapper,
    models::user_markets::{Column, Entity},
};

pub struct PostgresUserMarketReadRepository {
    db: DatabaseConnection,
}

impl PostgresUserMarketReadRepository {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

#[async_trait]
impl UserMarketReadRepository for PostgresUserMarketReadRepository {
    async fn find_by_id(
        &self,
        id: &UserMarketId,
    ) -> Result<Option<DomainUserMarket>, RepositoryError> {
        let model = Entity::find_by_id(id.get_id())
            .one(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        match model {
            Some(model) => {
                let mapper = UserMarketDbModelMapper::new(model);
                Ok(Some(mapper.into()))
            }
            None => Ok(None),
        }
    }

    async fn find_all(&self) -> Result<Vec<DomainUserMarket>, RepositoryError> {
        let models = Entity::find()
            .all(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        let user_markets = models
            .into_iter()
            .map(|model| {
                let mapper = UserMarketDbModelMapper::new(model);
                mapper.into()
            })
            .collect();

        Ok(user_markets)
    }

    async fn find_by_user_id(
        &self,
        user_id: &UserId,
    ) -> Result<Vec<DomainUserMarket>, RepositoryError> {
        let models = Entity::find()
            .filter(Column::UserId.eq(user_id.get_id()))
            .all(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        let user_markets = models
            .into_iter()
            .map(|model| {
                let mapper = UserMarketDbModelMapper::new(model);
                mapper.into()
            })
            .collect();

        Ok(user_markets)
    }

    async fn find_by_market_type(
        &self,
        market_type: &MarketType,
    ) -> Result<Vec<DomainUserMarket>, RepositoryError> {
        let db_market_type = match market_type {
            MarketType::Vending => crate::models::sea_orm_active_enums::MarketType::Vending,
            MarketType::Auction => crate::models::sea_orm_active_enums::MarketType::Auction,
        };

        let models = Entity::find()
            .filter(Column::Type.eq(db_market_type))
            .all(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        let user_markets = models
            .into_iter()
            .map(|model| {
                let mapper = UserMarketDbModelMapper::new(model);
                mapper.into()
            })
            .collect();

        Ok(user_markets)
    }

    async fn find_by_buyer_id(
        &self,
        buyer_id: &UserId,
    ) -> Result<Vec<DomainUserMarket>, RepositoryError> {
        let models = Entity::find()
            .filter(Column::BuyerId.eq(buyer_id.get_id()))
            .all(&self.db)
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        let user_markets = models
            .into_iter()
            .map(|model| {
                let mapper = UserMarketDbModelMapper::new(model);
                mapper.into()
            })
            .collect();

        Ok(user_markets)
    }
}
