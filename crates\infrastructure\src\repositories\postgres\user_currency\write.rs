use reforged_domain::error::RepositoryError;
use reforged_domain::models::user_currency::entity::UserCurrency;
use reforged_domain::models::user_currency::value_object::UserCurrencyId;
use reforged_domain::repository::user_currency_repository::UserCurrencyRepository;
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::{
    ActiveModelTrait, DatabaseConnection, EntityTrait, IntoActiveModel,
    prelude::async_trait::async_trait,
};

use crate::SeaORMErr;
use crate::models::user_currencies::{
    ActiveModel as UserCurrenciesActiveModel, Entity as UserCurrencies,
};

#[allow(dead_code)]
pub struct PostgresUserCurrencyRepository {
    pool: DatabaseConnection,
}

impl PostgresUserCurrencyRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserCurrencyRepository for PostgresUserCurrencyRepository {
    async fn save(&self, entity: &UserCurrency) -> Result<(), RepositoryError> {
        let model = UserCurrenciesActiveModel {
            id: Set(entity.id().get_id()),
            user_id: Set(entity.user_id().get_id()),
            gold: Set(entity.gold().value()),
            coins: Set(entity.coins().value()),
            diamonds: Set(entity.diamonds().value()),
            crystal: Set(entity.crystal().value()),
        };

        model.insert(&self.pool).await.map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn update(&self, entity: &UserCurrency) -> Result<(), RepositoryError> {
        let existing_model = UserCurrencies::find_by_id(entity.id().get_id())
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?
            .ok_or(RepositoryError::NotFound(format!(
                "UserCurrency with id {}",
                entity.id().get_id()
            )))?;

        let mut active_model = existing_model.into_active_model();

        active_model.user_id = Set(entity.user_id().get_id());
        active_model.gold = Set(entity.gold().value());
        active_model.coins = Set(entity.coins().value());
        active_model.diamonds = Set(entity.diamonds().value());
        active_model.crystal = Set(entity.crystal().value());

        active_model
            .update(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn delete(&self, id: &UserCurrencyId) -> Result<(), RepositoryError> {
        let result = UserCurrencies::delete_by_id(id.get_id())
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if result.rows_affected == 0 {
            return Err(RepositoryError::NotFound(format!(
                "UserCurrency with id {}",
                id.get_id()
            )));
        }

        Ok(())
    }
}
