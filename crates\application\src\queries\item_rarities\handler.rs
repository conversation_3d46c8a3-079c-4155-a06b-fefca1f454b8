use std::sync::Arc;

use async_trait::async_trait;
use reforged_domain::{
    models::item_rarity::{entity::ItemRarity, value_object::ItemRarityId},
    repository::item_rarity_repository::ItemRarityReadRepository,
};
use reforged_shared::{IdTrait, uuid_generator::uuid_to_u64};
use serde::Serialize;

use crate::{
    queries::item_rarities::queries::{GetAllItemRaritiesQuery, GetItemRarityByIdQuery},
    traits::QueryHandler,
};

#[derive(Clone)]
pub struct ItemRarityQueryHandler {
    item_rarity_repository: Arc<dyn ItemRarityReadRepository>,
}

impl ItemRarityQueryHandler {
    pub fn new(item_rarity_repository: Arc<dyn ItemRarityReadRepository>) -> Self {
        Self {
            item_rarity_repository,
        }
    }
}

#[async_trait]
impl QueryHandler<GetAllItemRaritiesQuery> for ItemRarityQueryHandler {
    type Output = Vec<ItemRarityResponse>;

    async fn handle(
        &self,
        _query: GetAllItemRaritiesQuery,
    ) -> Result<Vec<ItemRarityResponse>, crate::error::ApplicationError> {
        let item_rarities = self
            .item_rarity_repository
            .find_all()
            .await?
            .into_iter()
            .map(ItemRarityResponse::from)
            .collect();

        Ok(item_rarities)
    }
}

#[async_trait]
impl QueryHandler<GetItemRarityByIdQuery> for ItemRarityQueryHandler {
    type Output = ItemRarityResponse;

    async fn handle(
        &self,
        query: GetItemRarityByIdQuery,
    ) -> Result<ItemRarityResponse, crate::error::ApplicationError> {
        let id = ItemRarityId::new(query.id);
        let item_rarity = self.item_rarity_repository.find_by_id(&id).await?.ok_or(
            crate::error::ApplicationError::EntityNotFound(format!(
                "Item rarity with ID {} not found",
                query.id
            )),
        )?;

        Ok(ItemRarityResponse::from(item_rarity))
    }
}

#[derive(Serialize)]
pub struct ItemRarityResponse {
    id: uuid::Uuid,
    pid: u64,
    name: String,
}

impl From<ItemRarity> for ItemRarityResponse {
    fn from(value: ItemRarity) -> Self {
        let pid = uuid_to_u64(&value.id().get_id());

        Self {
            id: value.id().get_id(),
            pid,
            name: value.name().to_string(),
        }
    }
}
