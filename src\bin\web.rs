use actix_web::{HttpServer, dev::ServerHandle};
use reforged_infrastructure::{Config, web::WebConfig};
use reforged_web::web::create_web_service;
use tokio::signal;
use tracing::info;
use tracing_log::LogTracer;
use tracing_subscriber::{Layer, layer::SubscriberExt};

#[actix_web::main]
async fn main() -> anyhow::Result<()> {
    setup_tracing();
    setup_rustls();

    let web_config = WebConfig::from_env()?;
    let addrs = web_config.get_connection_string();

    let server = HttpServer::new(move || create_web_service())
        .workers(5)
        .bind(&addrs)?
        .shutdown_timeout(5)
        .run();

    let server_handle = server.handle();

    tokio::spawn(async move {
        shutdown_signal(server_handle).await;
    });

    info!("listening on {}", addrs);

    server.await?;

    Ok(())
}

pub fn setup_tracing() {
    let crate_name = env!("CARGO_CRATE_NAME");
    let crate_version = env!("CARGO_PKG_VERSION");

    let filter_layer = tracing_subscriber::EnvFilter::try_from_default_env().unwrap_or_else(|_| {
        format!(
            "RUST_LOG=info,{}=info,reforged_web=info,reforged-web=info,tokio=trace,runtime=trace,actix_web=info",
            crate_name
        )
        .into()
    });

    let fmt_layer = tracing_subscriber::fmt::layer().with_filter(filter_layer);
    let subscriber = tracing_subscriber::registry().with(fmt_layer);

    tracing::subscriber::set_global_default(subscriber)
        .expect("Failed to set global default subscriber");

    LogTracer::init().expect("Failed to set logger");

    info!("[REFORGED-WEB] {} v{}", crate_name, crate_version);
}

pub async fn shutdown_signal(handle: ServerHandle) {
    let ctrl_c = async {
        signal::ctrl_c()
            .await
            .expect("failed to initialize Ctrl+C handler");
    };

    #[cfg(unix)]
    let terminate = async {
        signal::unix::signal(signal::unix::SignalKind::terminate())
            .expect("failed to initialize signal handler")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();

    tokio::select! {
        _ = ctrl_c => {},
        _ = terminate => {},
    }

    info!("gracefully shutting down server...");
    info!("server shutdown complete");
    info!("goodbye!");

    handle.stop(true).await;
}

pub fn setup_rustls() {
    rustls::crypto::ring::default_provider()
        .install_default()
        .expect("Failed to install rustls crypto provider");
}
